using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services.Capl;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Alsi.Tab.Kit.Core.Services
{
    public class CinParameterService
    {
        private readonly CinTemplatesService _cinTemplatesService;

        public CinParameterService()
        {
            _cinTemplatesService = new CinTemplatesService();
        }

        /// <summary>
        /// 解析 CIN 文件参数
        /// </summary>
        public CinParameterParseResponse ParseCinFile(string sourceType, Guid? templateId, string filePath)
        {
            var targetFilePath = string.Empty;

            if (sourceType == "template")
            {
                if (!templateId.HasValue)
                {
                    throw new AppException("模板ID不能为空");
                }

                var template = _cinTemplatesService.GetCinTemplateById(templateId.Value);
                if (template == null)
                {
                    throw new AppException($"找不到ID为 '{templateId}' 的模板");
                }

                if (!template.FileExists)
                {
                    throw new AppException($"模板文件不存在: {template.FullPath}");
                }

                targetFilePath = template.FullPath;
            }
            else if (sourceType == "file")
            {
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    throw new AppException("文件路径不能为空");
                }

                if (!File.Exists(filePath))
                {
                    throw new AppException($"文件不存在: {filePath}");
                }

                targetFilePath = filePath;
            }
            else
            {
                throw new AppException($"不支持的源类型: {sourceType}");
            }

            // 使用 CaplUtils 解析文件
            var parseResult = CaplUtils.ParseFile(targetFilePath);
            if (!parseResult.Success)
            {
                throw new AppException($"解析CIN文件失败: {parseResult.ErrorMessage}");
            }

            // 提取所有变量
            var variables = new List<CaplVariable>();
            foreach (var codeBlock in parseResult.CodeBlocks)
            {
                if (codeBlock.ParsedVariables != null)
                {
                    variables.AddRange(codeBlock.ParsedVariables);
                }
            }


            return new CinParameterParseResponse
            {
                SourceFilePath = targetFilePath,
                Variables = variables
            };
        }

        /// <summary>
        /// 处理 CIN 文件参数替换
        /// </summary>
        public CinParameterProcessResponse ProcessCinFile(CinParameterRequest request)
        {
            var response = new CinParameterProcessResponse();

            // 首先解析文件获取源文件路径
            var parseResponse = ParseCinFile(request.SourceType, request.TemplateId, request.FilePath);

            var sourceFilePath = parseResponse.SourceFilePath;

            // 读取源文件内容
            var content = File.ReadAllText(sourceFilePath, Encoding.GetEncoding("GBK"));

            // 执行参数替换
            var processedContent = content;


            foreach (var parameterValue in request.ParameterValues)
            {
                var parameterName = parameterValue.Key;
                var newValue = parameterValue.Value;

                processedContent = CaplUtils.ReplaceVariableValue(processedContent, parameterName, newValue);
            }

            // 生成输出文件路径
            var sourceFileName = Path.GetFileNameWithoutExtension(sourceFilePath);
            var sourceDirectory = Path.GetDirectoryName(sourceFilePath);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var outputFileName = $"{sourceFileName}_{timestamp}.cin";
            var outputFilePath = Path.Combine(sourceDirectory, outputFileName);

            // 写入处理后的内容
            File.WriteAllText(outputFilePath, processedContent, Encoding.GetEncoding("GBK"));

            response.OutputFilePath = outputFilePath;
            return response;
        }
    }
}
