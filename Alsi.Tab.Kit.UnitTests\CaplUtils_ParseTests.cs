using Alsi.Tab.Kit.Core.Services.Capl;

namespace Alsi.Tab.Kit.UnitTests;

public class CaplUtils_ParseTests
{
    public CaplUtils_ParseTests()
    {
        CaplUtils.Initialize();
    }

    [Fact]
    public void ParseToStructure_ShouldReturnSuccessForValidFile()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ErrorMessage.ShouldBeNullOrEmpty();
        result.CodeBlocks.ShouldNotBeNull();
        result.CodeBlocks.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public void ParseToStructure_ShouldIdentifyIncludesBlock()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var includesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Include);
        includesBlock.ShouldNotBeNull();
        includesBlock.Content.ShouldContain("includes");
        includesBlock.Content.ShouldContain("#include");
    }

    [Fact]
    public void ParseToStructure_ShouldIdentifyVariablesBlock()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.Content.ShouldContain("variables");
        variablesBlock.Variables.ShouldNotBeNull();
        variablesBlock.Variables.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public void ParseToStructure_ShouldExtractVariableDeclarations()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        // 验证变量声明

        var variableNames = new[]
{
            "ECU_NAME_STR",
            "MSGID_CTRL_BUSLOAD",
            "REQ_ReadDID_ResIsMF",
            "IS_ECU_GW",
            "BUS_CAN_CANFD",
            "APP_IDs",
            "supportSIDandSubIDs",
            "PIN_CODES",
            "REQUIRED_NUM_OF_SWDL_ITERATIONS",
            "IS_RID0205_NEED_SA",
            "NM_ID"
        };
        variablesBlock.Variables.Count.ShouldBe(variableNames.Length);
        var multipleLinesA = string.Join(Environment.NewLine, variablesBlock.ParsedVariables.OrderBy(x => x.Name).Select(x => x.Name));
        var multipleLinesB = string.Join(Environment.NewLine, variableNames.OrderBy(x => x));
        multipleLinesA.ShouldBe(multipleLinesB);
    }

    [Fact]
    public void ParseToStructure_ShouldMaintainLineNumbers()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();

        foreach (var block in result.CodeBlocks)
        {
            block.StartLine.ShouldBeGreaterThan(0);
            block.EndLine.ShouldBeGreaterThanOrEqualTo(block.StartLine);
        }
    }

    [Fact]
    public void ParseToStructure_ShouldHandleCommentsCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo1.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ProcessedContent.ShouldNotBeNull();

        // 处理后的内容不应包含注释标记，但应保持行数
        var originalLines = System.IO.File.ReadAllLines(caplPath);
        var processedLines = result.ProcessedContent.Split('\n');
        processedLines.Length.ShouldBe(originalLines.Length);
    }

    [Fact]
    public void ParseToStructure_ShouldHandleComplexVariableDeclarations()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo1.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        // 验证复杂的变量声明（如数组、结构体）
        variablesBlock.Variables.ShouldContain(v => v.Contains("APP_IDs"));
        variablesBlock.Variables.ShouldContain(v => v.Contains("supportSIDandSubIDs"));
        variablesBlock.Variables.ShouldContain(v => v.Contains("PIN_CODES"));
    }

    [Fact]
    public void ParseToStructure_ShouldReturnErrorForNonExistentFile()
    {
        // Arrange
        var nonExistentPath = "./NonExistent.cin";

        // Act
        Should.Throw<Exception>(() =>
        {
            CaplUtils.ParseFile(nonExistentPath);
        });
    }

    [Fact]
    public void ParseToStructure_ShouldHandleEmptyVariablesBlock()
    {
        var testContent = @"/*@!Encoding:936*/
includes{
    #include ""test.cin""
}

variables{
}";

        // Act
        var result = CaplUtils.ParseContent(testContent);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.Variables.ShouldBeEmpty();
    }

    [Fact]
    public void ParseToStructure_ShouldParseVariableDetails()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.ParsedVariables.ShouldNotBeNull();
        variablesBlock.ParsedVariables.Count.ShouldBeGreaterThan(0);

        // 验证特定变量的解析
        var ecuNameVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "ECU_NAME_STR");
        ecuNameVar.ShouldNotBeNull();
        ecuNameVar.Type.ShouldBe("char");
        ecuNameVar.IsArray.ShouldBeTrue();
        ecuNameVar.ArraySize.ShouldBe("40");
        ecuNameVar.IsConst.ShouldBeFalse();
        ecuNameVar.Value.ShouldBe("\"BasicDiagnosticsEcu\"");
    }

    [Fact]
    public void ParseToStructure_ShouldParseConstVariables()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var constVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "MSGID_CTRL_BUSLOAD");
        constVar.ShouldNotBeNull();
        constVar.Type.ShouldBe("dword");
        constVar.IsConst.ShouldBeTrue();
        constVar.IsArray.ShouldBeFalse();
        constVar.Value.ShouldBe("0x1");
    }

    [Fact]
    public void ParseToStructure_ShouldParseArrayVariables()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var arrayVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "APP_IDs");
        arrayVar.ShouldNotBeNull();
        arrayVar.Type.ShouldBe("dword");
        arrayVar.IsArray.ShouldBeTrue();
        arrayVar.ArraySize.ShouldBe("7");
        arrayVar.Value.ShouldContain("{0x3F,0x57,0x158,0x173,0x22E,0x2D1,0x510}");
    }

    [Fact]
    public void ParseToStructure_ShouldParseStructArrayVariables()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CaplUtils.ParseFile(caplPath);

        // Assert
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CaplContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var structVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "supportSIDandSubIDs");
        structVar.ShouldNotBeNull();
        structVar.Type.ShouldBe("struct STRU_SID_SUBID");
        structVar.IsArray.ShouldBeTrue();
        structVar.ArraySize.ShouldBe("20");
        structVar.Value.ShouldContain("{7,0x10,3,{1,2,3}}");
    }
}