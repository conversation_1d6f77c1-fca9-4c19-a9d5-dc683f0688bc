<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>

    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;NU1701</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <NoWarn>1701;1702;NU1701</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;NU1701</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <NoWarn>1701;1702;NU1701</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.1.0" />
    <PackageReference Include="Shouldly" Version="4.3.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="5.0.0" />
    <PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="3.1.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Alsi.Tab.Kit.Core\Alsi.Tab.Kit.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Arxml\Files\SDB22400_DDM_221218_AR-4.2.2_Unflattened.arxml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Arxml\Files\SDB2243301_ZCUDM_240409_AR-4.2.2_Unflattened.arxml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="CaplResources\ParamDemo1.cin">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="CaplResources\ParamDemo2.cin">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
