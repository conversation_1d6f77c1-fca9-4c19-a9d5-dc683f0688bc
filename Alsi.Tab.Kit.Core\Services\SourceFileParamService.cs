using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services.Capl;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Alsi.Tab.Kit.Core.Services
{
    public class SourceFileParamService
    {
        public async Task<List<ParsedParam>> ParseSingleFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var collection = new CaplParamLoader().Load(filePath);
                    return ConvertToParsedParams(collection, Path.GetFileName(filePath));
                }
                catch (Exception ex)
                {
                    throw new Exception($"解析文件 {filePath} 失败: {ex.Message}", ex);
                }
            });
        }

        /// <summary>
        /// 将 IParamCollection 转换为 ParsedParam 列表
        /// </summary>
        /// <param name="collection">参数集合</param>
        /// <param name="source">来源文件名</param>
        /// <returns>ParsedParam 列表</returns>
        private List<ParsedParam> ConvertToParsedParams(IParamCollection collection, string source)
        {
            var parsedParams = new List<ParsedParam>();

            var ecus = collection.GetEcuNodes();
            foreach (var ecu in ecus)
            {
                foreach (var regularParamValueSource in ecu.RegularParamValueSources)
                {
                    foreach (var valueSource in regularParamValueSource.GetValueSources())
                    {
                        var parsedParam = new ParsedParam
                        {
                            EcuName = ecu.EcuNode,
                            Name = regularParamValueSource.RegularParamKey,
                            Value = ConvertParamValue(valueSource.Value),
                            ParamType = DetermineParamType(valueSource.Value),
                            Source = source,
                            Description = "TODO: Description"
                        };

                        parsedParams.Add(parsedParam);
                    }


                }
            }

            return parsedParams;
        }

        /// <summary>
        /// 转换参数值
        /// </summary>
        /// <param name="param">参数对象</param>
        /// <returns>转换后的值</returns>
        private object ConvertParamValue(string paramValue)
        {
            if (paramValue == null)
                return string.Empty;

            // 如果是复杂对象，尝试序列化为JSON
            if (paramValue is string stringValue)
            {
                return stringValue;
            }
            else if (paramValue.GetType().IsPrimitive || paramValue is decimal)
            {
                return paramValue;
            }
            else
            {
                // 复杂对象转换为JSON字符串
                try
                {
                    return Newtonsoft.Json.JsonConvert.SerializeObject(paramValue, Newtonsoft.Json.Formatting.Indented);
                }
                catch
                {
                    return paramValue.ToString();
                }
            }
        }

        /// <summary>
        /// 确定参数类型
        /// </summary>
        /// <param name="param">参数对象</param>
        /// <returns>参数类型</returns>
        private ParamType DetermineParamType(string paramValue)
        {
            if (paramValue == null)
                return ParamType.String;

            var valueType = paramValue.GetType();

            if (valueType == typeof(string))
            {
                var stringValue = paramValue as string;
                // 检查是否是JSON格式
                if (!string.IsNullOrEmpty(stringValue) &&
                    (stringValue.TrimStart().StartsWith("{") || stringValue.TrimStart().StartsWith("[")))
                {
                    try
                    {
                        Newtonsoft.Json.JsonConvert.DeserializeObject(stringValue);
                        return ParamType.Json;
                    }
                    catch
                    {
                        return ParamType.String;
                    }
                }
                return ParamType.String;
            }
            else if (valueType == typeof(int) || valueType == typeof(long) || valueType == typeof(short))
            {
                return ParamType.Integer;
            }
            else if (valueType == typeof(double) || valueType == typeof(float) || valueType == typeof(decimal))
            {
                return ParamType.Double;
            }
            else if (valueType == typeof(bool))
            {
                return ParamType.Boolean;
            }
            else if (valueType.IsArray)
            {
                return ParamType.Array;
            }
            else if (valueType.IsClass && valueType != typeof(string))
            {
                return ParamType.Object;
            }
            else
            {
                return ParamType.String;
            }
        }

        public static SourceFileType GetSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new AppException($"The file path is empty: {filePath}");
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            switch (extension)
            {
                case ".arxml":
                    return SourceFileType.Arxml;
                case ".sddb":
                    return SourceFileType.Sddb;
                case ".ldf":
                    return SourceFileType.Ldf;
                default:
                    throw new AppException($"Can't parse file with extension: {extension}");
            }
        }
    }
}
