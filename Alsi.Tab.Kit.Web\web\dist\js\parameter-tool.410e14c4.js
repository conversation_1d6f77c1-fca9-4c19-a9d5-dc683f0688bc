"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[771],{9922:function(e,a,l){l.r(a),l.d(a,{default:function(){return U}});var t=l(6768),r=l(4232);const s={class:"parameter-tool"},o={class:"main-content"},n={class:"cin-operations-section"},u={class:"cin-operations-content"},i={class:"operation-row"},c={key:0,class:"search-bar"},d={class:"filter-info"},p={key:0,class:"import-info"},v={class:"parameters-section"},m={class:"current-value"},h={key:0,class:"parameter-source"},g={key:1,class:"no-source"},y={class:"data-files-section"},f={class:"section-header"},k={class:"header-actions"},w={class:"data-files-table"},b=["title"],F={class:"name"},_={class:"action-buttons"};function C(e,a,l,C,P,V){const S=(0,t.g2)("el-cascader"),L=(0,t.g2)("el-button"),T=(0,t.g2)("el-input"),E=(0,t.g2)("el-table-column"),W=(0,t.g2)("document"),D=(0,t.g2)("el-icon"),K=(0,t.g2)("el-table"),R=(0,t.g2)("el-tag"),x=(0,t.g2)("ParamParseDialog");return(0,t.uX)(),(0,t.CE)("div",s,[(0,t.Lk)("div",o,[(0,t.Lk)("div",n,[a[5]||(a[5]=(0,t.Lk)("div",{class:"section-header"},[(0,t.Lk)("h4",null,"CIN 文件操作")],-1)),(0,t.Lk)("div",u,[(0,t.Lk)("div",i,[(0,t.bF)(S,{modelValue:e.selectedTemplatePath,"onUpdate:modelValue":a[0]||(a[0]=a=>e.selectedTemplatePath=a),options:e.cascaderOptions,placeholder:"选择 CIN 模板",style:{width:"300px"},onChange:e.handleTemplateChange,clearable:""},null,8,["modelValue","options","onChange"]),(0,t.bF)(L,{onClick:e.selectLocalFile,style:{"margin-left":"16px"}},{default:(0,t.k6)(()=>a[3]||(a[3]=[(0,t.eW)("选择本地 CIN 文件")])),_:1,__:[3]},8,["onClick"]),(0,t.bF)(L,{type:"primary",onClick:e.exportCinFile,loading:e.processing,style:{"margin-left":"16px"}},{default:(0,t.k6)(()=>a[4]||(a[4]=[(0,t.eW)("导出CIN")])),_:1,__:[4]},8,["onClick","loading"])])])]),e.variables.length>0?((0,t.uX)(),(0,t.CE)("div",c,[(0,t.bF)(T,{modelValue:e.searchKeyword,"onUpdate:modelValue":a[1]||(a[1]=a=>e.searchKeyword=a),placeholder:"搜索参数...",clearable:"",style:{width:"300px"}},null,8,["modelValue"]),(0,t.Lk)("div",d,[(0,t.Lk)("span",null,"显示 "+(0,r.v_)(e.filteredVariables.length)+" / "+(0,r.v_)(e.variables.length)+" 个参数",1),e.importedFilesCount>0?((0,t.uX)(),(0,t.CE)("span",p," 已导入 "+(0,r.v_)(e.importedFilesCount)+" 个文件的参数 ",1)):(0,t.Q3)("",!0)])])):(0,t.Q3)("",!0),(0,t.Lk)("div",v,[(0,t.bF)(K,{data:e.filteredVariables,border:"",style:{width:"100%",height:"100%"},size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{prop:"name",label:"参数名",width:"200","show-overflow-tooltip":""}),(0,t.bF)(E,{prop:"type",label:"类型",width:"120","show-overflow-tooltip":""}),(0,t.bF)(E,{prop:"value",label:"当前值",width:"200","show-overflow-tooltip":""},{default:(0,t.k6)(e=>[(0,t.Lk)("span",m,(0,r.v_)(e.row.value||"(空)"),1)]),_:1}),(0,t.bF)(E,{label:"新值","min-width":"200"},{default:(0,t.k6)(a=>[(0,t.bF)(T,{modelValue:e.parameterValues[a.row.name],"onUpdate:modelValue":l=>e.parameterValues[a.row.name]=l,placeholder:"输入新值",size:"small",onChange:l=>e.onParameterChange(a.row.name)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),(0,t.bF)(E,{label:"来源",width:"150","show-overflow-tooltip":""},{default:(0,t.k6)(a=>[e.getParameterSource(a.row.name)?((0,t.uX)(),(0,t.CE)("div",h,[(0,t.bF)(D,null,{default:(0,t.k6)(()=>[(0,t.bF)(W)]),_:1}),(0,t.Lk)("span",null,(0,r.v_)(e.getParameterSource(a.row.name)),1)])):((0,t.uX)(),(0,t.CE)("span",g,"CIN 文件"))]),_:1})]),_:1},8,["data"])]),(0,t.Lk)("div",y,[(0,t.Lk)("div",f,[a[8]||(a[8]=(0,t.Lk)("h4",null,"数据文件 (ARXML/SDDB/LDF)",-1)),(0,t.Lk)("div",k,[(0,t.bF)(L,{size:"small",type:"primary",onClick:e.selectDataFiles},{default:(0,t.k6)(()=>a[6]||(a[6]=[(0,t.eW)("添加文件")])),_:1,__:[6]},8,["onClick"]),e.importedFiles.length>0?((0,t.uX)(),(0,t.Wv)(L,{key:0,size:"small",type:"danger",onClick:e.clearAllDataFiles},{default:(0,t.k6)(()=>a[7]||(a[7]=[(0,t.eW)("清空")])),_:1,__:[7]},8,["onClick"])):(0,t.Q3)("",!0)])]),(0,t.Lk)("div",w,[(0,t.bF)(K,{data:e.importedFiles,border:"",style:{width:"100%"},size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{label:"类型",width:"80"},{default:(0,t.k6)(a=>[(0,t.bF)(R,{type:e.getFileTypeTagType(a.row.fileType),size:"small"},{default:(0,t.k6)(()=>[(0,t.eW)((0,r.v_)(a.row.fileType.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(E,{label:"文件名","min-width":"200"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",{class:"file-name",title:e.row.path},[(0,t.Lk)("span",F,(0,r.v_)(e.row.fileName),1)],8,b)]),_:1}),(0,t.bF)(E,{label:"状态",width:"100"},{default:(0,t.k6)(a=>[(0,t.bF)(R,{type:e.getStatusTagType(a.row.status),size:"small"},{default:(0,t.k6)(()=>[(0,t.eW)((0,r.v_)(e.getStatusText(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(E,{label:"操作",width:"300"},{default:(0,t.k6)(l=>[(0,t.Lk)("div",_,[l.row.status===e.SourceFileStatus.Pending||l.row.status===e.SourceFileStatus.Error?((0,t.uX)(),(0,t.Wv)(L,{key:0,type:"primary",size:"small",onClick:a=>e.parseDataFile(l.row.id)},{default:(0,t.k6)(()=>a[9]||(a[9]=[(0,t.eW)(" 解析参数 ")])),_:2,__:[9]},1032,["onClick"])):(0,t.Q3)("",!0),l.row.status===e.SourceFileStatus.Parsed?((0,t.uX)(),(0,t.Wv)(L,{key:1,type:"success",size:"small",onClick:a=>e.viewDataFileDetails(l.row)},{default:(0,t.k6)(()=>a[10]||(a[10]=[(0,t.eW)(" 查看结果 ")])),_:2,__:[10]},1032,["onClick"])):(0,t.Q3)("",!0),(0,t.bF)(L,{type:"warning",size:"small",onClick:a=>e.openDataFileFolder(l.row.path)},{default:(0,t.k6)(()=>a[11]||(a[11]=[(0,t.eW)(" 打开文件夹 ")])),_:2,__:[11]},1032,["onClick"]),(0,t.bF)(L,{type:"danger",size:"small",onClick:a=>e.removeDataFile(l.row.id)},{default:(0,t.k6)(()=>a[12]||(a[12]=[(0,t.eW)(" 移除 ")])),_:2,__:[12]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])])])]),(0,t.bF)(x,{modelValue:e.parseDialogVisible,"onUpdate:modelValue":a[2]||(a[2]=a=>e.parseDialogVisible=a),"source-file":e.currentParseFile,"parsed-params":e.currentParsedParams,onApplyParams:e.applyParsedParams},null,8,["modelValue","source-file","parsed-params","onApplyParams"])])}l(4114),l(8111),l(2489),l(116),l(7588),l(1701),l(7642),l(8004),l(3853),l(5876),l(2475),l(5024),l(1698);var P=l(144),V=l(1021),S=l(1219),L=l(2933),T=l(7477);const E={class:"dialog-content"},W={class:"filter-section"},D={class:"param-list"},K={class:"param-value"},R={key:0},x={key:0,class:"batch-actions"},X={class:"dialog-footer"};var O=(0,t.pM)({__name:"ParamParseDialog",props:{modelValue:{type:Boolean},sourceFile:{},parsedParams:{}},emits:["update:modelValue","apply-params"],setup(e,{emit:a}){const l=e,s=a,o=(0,P.KR)(!1),n=(0,P.KR)(""),u=(0,P.KR)(""),i=(0,P.KR)([]),c=(0,P.KR)(!1),d=(0,P.KR)(""),p=(0,t.EW)(()=>{const e=new Set(l.parsedParams.map(e=>e.ecuName));return Array.from(e).sort()}),v=(0,t.EW)(()=>{let e=l.parsedParams;if(u.value&&(e=e.filter(e=>e.ecuName===u.value)),n.value){const a=n.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a)||e.description.toLowerCase().includes(a))}return e});(0,t.wB)(()=>l.modelValue,e=>{o.value=e,e&&(n.value="",i.value=[],p.value.length>0&&(u.value=p.value[0],h()))}),(0,t.wB)(o,e=>{s("update:modelValue",e)});const m=()=>{h()},h=()=>{i.value=[...v.value]},g=()=>{o.value=!1},y=e=>{i.value=e},f=e=>{switch(e){case V.aX.String:return"";case V.aX.Integer:return"success";case V.aX.Double:return"success";case V.aX.Boolean:return"warning";case V.aX.Json:return"primary";case V.aX.Array:return"info";case V.aX.Object:return"info";default:return""}},k=e=>null===e||void 0===e?"":"string"===typeof e?e.length>50?e.substring(0,50)+"...":e:String(e),w=e=>{try{if("string"===typeof e.value){const a=JSON.parse(e.value);d.value=JSON.stringify(a,null,2)}else d.value=JSON.stringify(e.value,null,2)}catch{d.value=String(e.value)}c.value=!0},b=()=>{0!==i.value.length?(s("apply-params",i.value),S.nk.success(`成功应用 ${i.value.length} 个参数`),g()):S.nk.warning("请先选择要应用的参数")};return(e,a)=>{const l=(0,t.g2)("el-option"),s=(0,t.g2)("el-select"),h=(0,t.g2)("el-icon"),F=(0,t.g2)("el-input"),_=(0,t.g2)("el-table-column"),C=(0,t.g2)("el-tag"),V=(0,t.g2)("el-button"),S=(0,t.g2)("el-table"),L=(0,t.g2)("el-alert"),O=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(O,{modelValue:o.value,"onUpdate:modelValue":a[4]||(a[4]=e=>o.value=e),title:"参数解析结果",width:"80%","before-close":g,"destroy-on-close":""},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",X,[(0,t.bF)(V,{onClick:g},{default:(0,t.k6)(()=>a[6]||(a[6]=[(0,t.eW)("取消")])),_:1,__:[6]}),(0,t.bF)(V,{type:"primary",onClick:b,disabled:0===i.value.length},{default:(0,t.k6)(()=>[(0,t.eW)(" 应用参数 ("+(0,r.v_)(i.value.length)+") ",1)]),_:1},8,["disabled"])])]),default:(0,t.k6)(()=>[(0,t.Lk)("div",E,[(0,t.Lk)("div",W,[(0,t.bF)(s,{modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),placeholder:"选择 ECU",style:{width:"200px"},onChange:m},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(p.value,e=>((0,t.uX)(),(0,t.Wv)(l,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),(0,t.bF)(F,{modelValue:n.value,"onUpdate:modelValue":a[1]||(a[1]=e=>n.value=e),placeholder:"搜索参数名称...",clearable:"",style:{width:"300px"}},{prefix:(0,t.k6)(()=>[(0,t.bF)(h,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,P.R1)(T.Search))]),_:1})]),_:1},8,["modelValue"])]),(0,t.Lk)("div",D,[(0,t.bF)(S,{data:v.value,height:"400",onSelectionChange:y,"row-key":"name"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{type:"selection",width:"55"}),(0,t.bF)(_,{prop:"name",label:"参数名",width:"200","show-overflow-tooltip":""}),(0,t.bF)(_,{prop:"paramType",label:"类型",width:"100"},{default:(0,t.k6)(({row:e})=>[(0,t.bF)(C,{type:f(e.paramType),size:"small"},{default:(0,t.k6)(()=>[(0,t.eW)((0,r.v_)(e.paramType),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(_,{prop:"value",label:"值","min-width":"200","show-overflow-tooltip":""},{default:(0,t.k6)(({row:e})=>[(0,t.Lk)("div",K,["Json"!==e.paramType?((0,t.uX)(),(0,t.CE)("span",R,(0,r.v_)(k(e.value)),1)):((0,t.uX)(),(0,t.Wv)(V,{key:1,type:"text",size:"small",onClick:a=>w(e)},{default:(0,t.k6)(()=>a[5]||(a[5]=[(0,t.eW)(" 查看JSON ")])),_:2,__:[5]},1032,["onClick"]))])]),_:1}),(0,t.bF)(_,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""})]),_:1},8,["data"])]),i.value.length>0?((0,t.uX)(),(0,t.CE)("div",x,[(0,t.bF)(L,{title:`已选择 ${i.value.length} 个参数`,type:"info",closable:!1,"show-icon":""},null,8,["title"])])):(0,t.Q3)("",!0)]),(0,t.bF)(O,{modelValue:c.value,"onUpdate:modelValue":a[3]||(a[3]=e=>c.value=e),title:"JSON 参数值",width:"60%","append-to-body":""},{default:(0,t.k6)(()=>[(0,t.bF)(F,{modelValue:d.value,"onUpdate:modelValue":a[2]||(a[2]=e=>d.value=e),type:"textarea",rows:15,readonly:"",style:{"font-family":"'Courier New', monospace"}},null,8,["modelValue"])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),I=l(1241);const Q=(0,I.A)(O,[["__scopeId","data-v-07b1abc3"]]);var N=Q,z=(0,t.pM)({name:"ParameterToolView",components:{ParamParseDialog:N,Document:T.Document},setup(){const e=(0,P.KR)([]),a=(0,P.KR)("template"),l=(0,P.KR)(""),r=(0,P.KR)(""),s=(0,P.KR)([]),o=(0,P.KR)(""),n=(0,P.KR)([]),u=(0,P.KR)({}),i=(0,P.KR)(""),c=(0,P.KR)(""),d=(0,P.KR)(!1),p=(0,P.KR)(!1),v=(0,P.KR)(!1),m=(0,P.KR)(null),h=(0,P.KR)([]),g=(0,P.KR)({}),y=(0,P.KR)([]),f=(0,P.KR)(new Set),k=(0,t.EW)(()=>{const a=new Set(e.value.map(e=>e.category));return Array.from(a).filter(e=>e)}),w=(0,t.EW)(()=>k.value.map(a=>({value:a,label:a,children:e.value.filter(e=>e.category===a).map(e=>({value:e.id,label:e.name}))}))),b=(0,t.EW)(()=>l.value?e.value.filter(e=>e.category===l.value):[]),F=(0,t.EW)(()=>{if(!c.value)return n.value;const e=c.value.toLowerCase();return n.value.filter(a=>a.name.toLowerCase().includes(e)||a.type.toLowerCase().includes(e)||a.value&&a.value.toLowerCase().includes(e))}),_=(0,t.EW)(()=>y.value.length),C=(0,t.EW)(()=>f.value.size),E=(0,t.EW)(()=>f.value.size>0),W=async()=>{try{const a=await V.GQ.cinParameter.getTemplates();e.value=a.data,k.value.length>0&&(l.value=k.value[0],K())}catch(a){console.error("加载模板失败:",a)}},D=()=>{n.value=[],u.value={},i.value="",f.value.clear()},K=()=>{b.value.length>0?r.value=b.value[0].id:r.value=""},R=async()=>{try{const e=await V.GQ.cinParameter.selectFile();o.value=e.data,await x()}catch(e){console.error("选择文件失败:",e)}},x=async()=>{if(o.value){d.value=!0;try{const e={sourceType:"file",filePath:o.value},a=await V.GQ.cinParameter.parseFile(e),l=a.data;n.value=l.variables,i.value=l.sourceFilePath,u.value={},l.variables.forEach(e=>{u.value[e.name]=e.value||""}),f.value.clear(),S.nk.success(`成功解析 ${l.variables.length} 个参数`)}catch(e){console.error("解析文件失败:",e),S.nk.error("解析文件失败")}finally{d.value=!1}}},X=e=>{e&&2===e.length&&(l.value=e[0],r.value=e[1],I())},O=a=>e.value.filter(e=>e.category===a),I=async()=>{if(r.value){d.value=!0;try{const e={sourceType:"template",templateId:r.value,filePath:""},a=await V.GQ.cinParameter.parseFile(e),l=a.data;n.value=l.variables,i.value=l.sourceFilePath,u.value={},l.variables.forEach(e=>{u.value[e.name]=e.value||""}),f.value.clear(),S.nk.success(`成功加载模板 ${l.variables.length} 个参数`)}catch(e){console.error("加载模板失败:",e),S.nk.error("加载模板失败")}finally{d.value=!1}}},Q=e=>{f.value.add(e)},N=async()=>{p.value=!0;try{const e={sourceType:a.value,templateId:"template"===a.value?r.value:void 0,filePath:"file"===a.value?o.value:"",parameterValues:u.value},l=await V.GQ.cinParameter.processFile(e),t=l.data;S.nk.success("文件导出成功！"),f.value.clear(),await V.GQ.explorer.openExplorer(t.outputFilePath)}catch(e){console.error("导出文件失败:",e),S.nk.error("导出文件失败")}finally{p.value=!1}},z=(e,a)=>{m.value=e,h.value=a,v.value=!0},G=e=>{if(e.forEach(e=>{const a=n.value.find(a=>a.name.toLowerCase()===e.name.toLowerCase());a&&(u.value[a.name]=String(e.value),g.value[a.name]=e.source,f.value.add(a.name))}),m.value){const e=y.value.findIndex(e=>e.id===m.value.id);e>=0?y.value[e]=m.value:y.value.push(m.value)}S.nk.success(`成功应用 ${e.length} 个参数`)},U=e=>g.value[e],A=e=>e&&e.split(/[\\/]/).pop()||"",J=e=>{switch(e){case V.yJ.Arxml:return"primary";case V.yJ.Sddb:return"success";case V.yJ.Ldf:return"warning";default:return""}},B=e=>{let a=0;return Object.keys(g.value).forEach(l=>{g.value[l]===e.fileName&&a++}),a},$=async e=>{try{await V.GQ.cinParameter.removeSourceFile({fileId:e});const a=y.value.findIndex(a=>a.id===e);a>=0&&(y.value.splice(a,1),S.nk.success("已移除数据文件"))}catch(a){console.error("移除文件失败:",a),S.nk.error("移除文件失败")}},M=async()=>{try{const e=await V.GQ.cinParameter.selectSourceFiles(),a=e.data;a&&a.length>0&&(await j(a),S.nk.success(`成功添加 ${a.length} 个参数数据文件`))}catch(e){console.error("选择文件失败:",e),S.nk.error("选择文件失败")}},j=async e=>{try{const a=await V.GQ.cinParameter.addSourceFiles({filePaths:e}),l=a.data;return l.forEach(e=>{const a=y.value.findIndex(a=>a.id===e.id);a>=0?y.value[a]=e:y.value.push(e)}),l}catch(a){throw console.error("添加文件失败:",a),a}},q=async e=>{try{const a=y.value.find(a=>a.id===e);a&&(a.status=V.O0.Parsing);const l=await V.GQ.cinParameter.parseSourceFile({fileId:e}),t=l.data,r=y.value.findIndex(a=>a.id===e);r>=0&&(y.value[r]=t),S.nk.success(`参数解析完成，共解析出 ${t.parsedParams?.length||0} 个参数`),t.parsedParams&&t.parsedParams.length>0&&(m.value=t,h.value=t.parsedParams,v.value=!0)}catch(a){console.error("解析文件失败:",a),S.nk.error("参数解析失败");const l=y.value.find(a=>a.id===e);l&&(l.status=V.O0.Error)}},H=e=>{e.parsedParams&&e.parsedParams.length>0?(m.value=e,h.value=e.parsedParams,v.value=!0):S.nk.warning("该文件暂无解析结果")},Y=async e=>{try{await V.GQ.explorer.openExplorer(e)}catch(a){console.error("打开文件夹失败:",a),S.nk.error("打开文件夹失败")}},Z=e=>{$(e)},ee=async()=>{try{await L.s.confirm("确定要清空所有参数数据文件吗？","确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await V.GQ.cinParameter.clearSourceFiles(),y.value=[],S.nk.success("已清空所有参数数据文件")}catch(e){"cancel"!==e&&(console.error("清空文件失败:",e),S.nk.error("清空文件失败"))}},ae=e=>{switch(e){case V.O0.Pending:return"";case V.O0.Parsing:return"warning";case V.O0.Parsed:return"success";case V.O0.Error:return"danger";default:return""}},le=e=>{switch(e){case V.O0.Pending:return"待解析";case V.O0.Parsing:return"解析中";case V.O0.Parsed:return"已解析";case V.O0.Error:return"解析失败";default:return e}},te=async()=>{try{const e=await V.GQ.cinParameter.getSourceFiles();y.value=e.data}catch(e){console.error("加载数据文件失败:",e),S.nk.error("加载数据文件失败")}};return(0,t.sV)(async()=>{await W(),await te()}),{templates:e,sourceType:a,selectedCategory:l,selectedTemplateId:r,selectedTemplatePath:s,filePath:o,variables:n,parameterValues:u,sourceFilePath:i,searchKeyword:c,parsing:d,processing:p,categories:k,cascaderOptions:w,filteredTemplates:b,filteredVariables:F,importedFilesCount:_,modifiedParamsCount:C,hasUnsavedChanges:E,importedFiles:y,onSourceTypeChange:D,onCategoryChange:K,selectLocalFile:R,handleTemplateChange:X,getTemplatesByCategory:O,loadSelectedTemplate:I,onParameterChange:Q,exportCinFile:N,getFileName:A,getFileTypeTagType:J,getAppliedParamsCount:B,removeImportedFile:$,selectDataFiles:M,parseDataFile:q,viewDataFileDetails:H,openDataFileFolder:Y,removeDataFile:Z,clearAllDataFiles:ee,getStatusTagType:ae,getStatusText:le,parseDialogVisible:v,currentParseFile:m,currentParsedParams:h,handleParseResult:z,applyParsedParams:G,getParameterSource:U,Document:T.Document,SourceFileStatus:V.O0}}});const G=(0,I.A)(z,[["render",C],["__scopeId","data-v-043d8365"]]);var U=G}}]);
//# sourceMappingURL=parameter-tool.410e14c4.js.map