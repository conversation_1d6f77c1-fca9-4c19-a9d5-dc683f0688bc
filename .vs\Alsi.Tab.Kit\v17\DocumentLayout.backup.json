{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.web\\controllers\\cinparametercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|solutionrelative:alsi.tab.kit.web\\controllers\\cinparametercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\sourcefileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\sourcefileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\services\\sourcefileparamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\sourcefileparamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.unittests\\arxml\\signalunittest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|solutionrelative:alsi.tab.kit.unittests\\arxml\\signalunittest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.unittests\\arxml\\arxmlunittest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|solutionrelative:alsi.tab.kit.unittests\\arxml\\arxmlunittest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\services\\capl\\caplparamloader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\capl\\caplparamloader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\models\\cintemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\models\\cintemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CinParameterController.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "RelativeToolTip": "Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "ViewState": "AgIAAF4AAAAAAAAAAAAAwG8AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:42:34.177Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "SignalUnitTest.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\Arxml\\SignalUnitTest.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.UnitTests\\Arxml\\SignalUnitTest.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\Arxml\\SignalUnitTest.cs", "RelativeToolTip": "Alsi.Tab.Kit.UnitTests\\Arxml\\SignalUnitTest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:40:21.848Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ArxmlUnitTest.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\Arxml\\ArxmlUnitTest.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.UnitTests\\Arxml\\ArxmlUnitTest.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\Arxml\\ArxmlUnitTest.cs", "RelativeToolTip": "Alsi.Tab.Kit.UnitTests\\Arxml\\ArxmlUnitTest.cs", "ViewState": "AgIAABoAAAAAAAAAAAAxwCkAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:40:12.584Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "CaplParamLoader.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamLoader.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamLoader.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamLoader.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamLoader.cs", "ViewState": "AgIAADUAAAAAAAAAAAAmwEIAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:38:10.088Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "SourceFileService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\SourceFileService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\SourceFileService.cs", "ViewState": "AgIAAIAAAAAAAAAAAAAowJEAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:36:13.47Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CinTemplate.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "ViewState": "AgIAAE4AAAAAAAAAAIA6wFwAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:19:05.096Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "SourceFileParamService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAmwAwAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:16:56.419Z"}]}]}]}