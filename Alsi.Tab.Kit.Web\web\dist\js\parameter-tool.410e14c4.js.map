{"version": 3, "file": "js/parameter-tool.410e14c4.js", "mappings": "0LAEA,MAAMA,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCAVD,MAAM,gBDCTE,EAAa,CCCRF,MAAM,0BDAXG,EAAa,CCKNH,MAAM,0BDJbI,EAAa,CCKJJ,MAAM,iBDJfK,EAAa,CACjBC,IAAK,ECqBIN,MAAM,cDlBXO,EAAa,CCyBNP,MAAM,eDxBbQ,EAAa,CACjBF,IAAK,ECyBuCN,MAAM,eDtB9CS,EAAa,CC6BRT,MAAM,sBD5BXU,EAAc,CCkCAV,MAAM,iBDjCpBW,EAAc,CAClBL,IAAK,EC+CsDN,MAAM,oBD5C7DY,EAAc,CAClBN,IAAK,EC+CoBN,MAAM,aD5C3Ba,EAAc,CCmDTb,MAAM,sBDlDXc,EAAc,CCmDPd,MAAM,kBDlDbe,EAAc,CCoDLf,MAAM,kBDnDfgB,EAAc,CCyDPhB,MAAM,oBDxDbiB,EAAc,CAAC,SACfC,EAAc,CCkEIlB,MAAM,QDjExBmB,EAAc,CC8ECnB,MAAM,kBD5ErB,SAAUoB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAA6BH,EAAAA,EAAAA,IAAkB,mBAC/CI,GAAsBJ,EAAAA,EAAAA,IAAkB,YACxCK,GAAqBL,EAAAA,EAAAA,IAAkB,WACvCM,GAAsBN,EAAAA,EAAAA,IAAkB,YACxCO,GAAoBP,EAAAA,EAAAA,IAAkB,UACtCQ,GAA8BR,EAAAA,EAAAA,IAAkB,oBAEtD,OAAQS,EAAAA,EAAAA,OC5CRC,EAAAA,EAAAA,IA8JM,MA9JNvC,EA8JM,EA5JJwC,EAAAA,EAAAA,IAmJM,MAnJNtC,EAmJM,EAjJJsC,EAAAA,EAAAA,IAqBM,MArBNrC,EAqBM,CDsBJoB,EAAO,KAAOA,EAAO,IC1CrBiB,EAAAA,EAAAA,IAEM,OAFDvC,MAAM,kBAAgB,EACzBuC,EAAAA,EAAAA,IAAiB,UAAb,cD2CF,KCxCJA,EAAAA,EAAAA,IAeM,MAfNpC,EAeM,EAdJoC,EAAAA,EAAAA,IAaM,MAbNnC,EAaM,EAZJoC,EAAAA,EAAAA,IAOEb,EAAA,CDmCAc,WCzCSpB,EAAAqB,qBD0CT,sBAAuBpB,EAAO,KAAOA,EAAO,GAAMqB,GC1CzCtB,EAAAqB,qBAAoBC,GAC5BC,QAASvB,EAAAwB,gBACVC,YAAY,YACZC,MAAA,gBACCC,SAAQ3B,EAAA4B,qBACTC,UAAA,ID2CC,KAAM,EAAG,CAAC,aAAc,UAAW,cCxCtCV,EAAAA,EAAAA,IAAsFX,EAAA,CAA1EsB,QAAO9B,EAAA+B,gBAAiBL,MAAA,wBD4CjC,CACDM,SAASC,EAAAA,EAAAA,IC7CoD,IAAWhC,EAAA,KAAAA,EAAA,KD8CtEiC,EAAAA,EAAAA,IC9C2D,kBDgD7DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,aChDPjB,EAAAA,EAAAA,IAAmHX,EAAA,CAAxG6B,KAAK,UAAWP,QAAO9B,EAAAsC,cAAgBC,QAASvC,EAAAwC,WAAYd,MAAA,wBDsDpE,CACDM,SAASC,EAAAA,EAAAA,ICvDuF,IAAKhC,EAAA,KAAAA,EAAA,KDwDnGiC,EAAAA,EAAAA,ICxD8F,YD0DhGC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,UAAW,kBCtDMpC,EAAAyC,UAAUC,OAAS,ID2D5C1B,EAAAA,EAAAA,OC3DLC,EAAAA,EAAAA,IAaM,MAbNjC,EAaM,EAZJmC,EAAAA,EAAAA,IAKEV,EAAA,CDuDIW,WC3DKpB,EAAA2C,cD4DL,sBAAuB1C,EAAO,KAAOA,EAAO,GAAMqB,GC5D7CtB,EAAA2C,cAAarB,GACtBG,YAAY,UACZI,UAAA,GACAH,MAAA,iBD6DK,KAAM,EAAG,CAAC,gBC3DjBR,EAAAA,EAAAA,IAKM,MALNhC,EAKM,EAJJgC,EAAAA,EAAAA,IAA2E,YAArE,OAAG0B,EAAAA,EAAAA,IAAG5C,EAAA6C,kBAAkBH,QAAS,OAAGE,EAAAA,EAAAA,IAAG5C,EAAAyC,UAAUC,QAAS,OAAI,GACxD1C,EAAA8C,mBAAqB,ID6DxB9B,EAAAA,EAAAA,OC7DTC,EAAAA,EAAAA,IAEO,OAFP9B,EAAwD,SAClDyD,EAAAA,EAAAA,IAAG5C,EAAA8C,oBAAqB,WAC9B,KD4DQC,EAAAA,EAAAA,IAAoB,IAAI,SAGhCA,EAAAA,EAAAA,IAAoB,IAAI,IC1D5B7B,EAAAA,EAAAA,IA6BM,MA7BN9B,EA6BM,EA5BJ+B,EAAAA,EAAAA,IA2BWN,EAAA,CA3BAmC,KAAMhD,EAAA6C,kBAAmBI,OAAA,GAAOvB,MAAA,6BAAmCwB,KAAK,SDgEhF,CACDlB,SAASC,EAAAA,EAAAA,IChET,IAA6E,EAA7Ed,EAAAA,EAAAA,IAA6ET,EAAA,CAA5DyC,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDlC,EAAAA,EAAAA,IAA4ET,EAAA,CAA3DyC,KAAK,OAAOC,MAAM,KAAKC,MAAM,MAAM,8BACpDlC,EAAAA,EAAAA,IAIkBT,EAAA,CAJDyC,KAAK,QAAQC,MAAM,MAAMC,MAAM,MAAM,4BDgFjD,CC/EQrB,SAAOC,EAAAA,EAAAA,IACiDqB,GAD1C,EACvBpC,EAAAA,EAAAA,IAAiE,OAAjE7B,GAAiEuD,EAAAA,EAAAA,IAAlCU,EAAMC,IAAIC,OAAS,OAAJ,KDkF9CrB,EAAG,KC/EPhB,EAAAA,EAAAA,IASkBT,EAAA,CATD0C,MAAM,KAAK,YAAU,ODoFjC,CCnFQpB,SAAOC,EAAAA,EAAAA,IAMdqB,GANqB,EACvBnC,EAAAA,EAAAA,IAKEV,EAAA,CDgFEW,WCpFOpB,EAAAyD,gBAAgBH,EAAMC,IAAIG,MDqFjC,sBAAwBpC,GCrFjBtB,EAAAyD,gBAAgBH,EAAMC,IAAIG,MAAIpC,EACvCG,YAAY,OACZyB,KAAK,QACJvB,SAAML,GAAEtB,EAAA2D,kBAAkBL,EAAMC,IAAIG,ODsFlC,KAAM,EAAG,CAAC,aAAc,sBAAuB,eAEpDvB,EAAG,KCpFPhB,EAAAA,EAAAA,IAQkBT,EAAA,CARD0C,MAAM,KAAKC,MAAM,MAAM,4BD0FnC,CCzFQrB,SAAOC,EAAAA,EAAAA,IAIVqB,GAJiB,CACZtD,EAAA4D,mBAAmBN,EAAMC,IAAIG,QD2FjC1C,EAAAA,EAAAA,OC3FPC,EAAAA,EAAAA,IAGM,MAHN3B,EAGM,EAFJ6B,EAAAA,EAAAA,IAA+BP,EAAA,MD4FvBoB,SAASC,EAAAA,EAAAA,IC5FR,IAAY,EAAZd,EAAAA,EAAAA,IAAYR,KD+FbwB,EAAG,KC9FXjB,EAAAA,EAAAA,IAAqD,aAAA0B,EAAAA,EAAAA,IAA5C5C,EAAA4D,mBAAmBN,EAAMC,IAAIG,OAAI,QDkGrC1C,EAAAA,EAAAA,OChGPC,EAAAA,EAAAA,IAA4C,OAA5C1B,EAA+B,aDkG/B4C,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,YC/FTjB,EAAAA,EAAAA,IAwEM,MAxEN1B,EAwEM,EAvEJ0B,EAAAA,EAAAA,IAMM,MANNzB,EAMM,CD4FJQ,EAAO,KAAOA,EAAO,ICjGrBiB,EAAAA,EAAAA,IAA8B,UAA1B,yBAAqB,KACzBA,EAAAA,EAAAA,IAGM,MAHNxB,EAGM,EAFJyB,EAAAA,EAAAA,IAAgFX,EAAA,CAArE0C,KAAK,QAAQb,KAAK,UAAWP,QAAO9B,EAAA6D,iBDqG5C,CACD7B,SAASC,EAAAA,EAAAA,ICtGqD,IAAIhC,EAAA,KAAAA,EAAA,KDuGhEiC,EAAAA,EAAAA,ICvG4D,WDyG9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,YC1GgEpC,EAAA8D,cAAcpB,OAAS,ID4GzF1B,EAAAA,EAAAA,OC5GL+C,EAAAA,EAAAA,IAA+GvD,EAAA,CD6GzGvB,IAAK,EC7GAiE,KAAK,QAAQb,KAAK,SAAUP,QAAO9B,EAAAgE,mBDiHvC,CACDhC,SAASC,EAAAA,EAAAA,IClHkF,IAAEhC,EAAA,KAAAA,EAAA,KDmH3FiC,EAAAA,EAAAA,ICnHyF,SDqH3FC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cACPW,EAAAA,EAAAA,IAAoB,IAAI,QCpHhC7B,EAAAA,EAAAA,IA8DM,MA9DNvB,EA8DM,EA7DJwB,EAAAA,EAAAA,IA4DWN,EAAA,CA5DAmC,KAAMhD,EAAA8D,cAAeb,OAAA,GAAOvB,MAAA,eAAoBwB,KAAK,SD4H7D,CACDlB,SAASC,EAAAA,EAAAA,IC5HT,IAIkB,EAJlBd,EAAAA,EAAAA,IAIkBT,EAAA,CAJD0C,MAAM,KAAKC,MAAM,MDgI7B,CC/HQrB,SAAOC,EAAAA,EAAAA,IACmGqB,GAD5F,EACvBnC,EAAAA,EAAAA,IAAmHL,EAAA,CAA1GuB,KAAMrC,EAAAiE,mBAAmBX,EAAMC,IAAIW,UAAWhB,KAAK,SDmIvD,CACDlB,SAASC,EAAAA,EAAAA,ICpIuD,IAAsC,EDqIpGC,EAAAA,EAAAA,KAAiBU,EAAAA,EAAAA,ICrIgDU,EAAMC,IAAIW,SAASC,eAAW,KDuIjGhC,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCtIPhB,EAAAA,EAAAA,IAMkBT,EAAA,CAND0C,MAAM,MAAM,YAAU,OD2IlC,CC1IQpB,SAAOC,EAAAA,EAAAA,IAGVqB,GAHiB,EACvBpC,EAAAA,EAAAA,IAEM,OAFDvC,MAAM,YAAayF,MAAOd,EAAMC,IAAIc,MD8IpC,EC7IHnD,EAAAA,EAAAA,IAAkD,OAAlDrB,GAAkD+C,EAAAA,EAAAA,IAA5BU,EAAMC,IAAIe,UAAQ,ID+IrC,EAAG1E,KAERuC,EAAG,KC5IPhB,EAAAA,EAAAA,IAIkBT,EAAA,CAJD0C,MAAM,KAAKC,MAAM,ODiJ7B,CChJQrB,SAAOC,EAAAA,EAAAA,IAC8FqB,GADvF,EACvBnC,EAAAA,EAAAA,IAA8GL,EAAA,CAArGuB,KAAMrC,EAAAuE,iBAAiBjB,EAAMC,IAAIiB,QAAStB,KAAK,SDoJnD,CACDlB,SAASC,EAAAA,EAAAA,ICrJmD,IAAqC,EDsJ/FC,EAAAA,EAAAA,KAAiBU,EAAAA,EAAAA,ICtJ4C5C,EAAAyE,cAAcnB,EAAMC,IAAIiB,SAAM,KDwJ7FrC,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCvJPhB,EAAAA,EAAAA,IAsCkBT,EAAA,CAtCD0C,MAAM,KAAKC,MAAM,OD4J7B,CC3JQrB,SAAOC,EAAAA,EAAAA,IAmCVqB,GAnCiB,EACvBpC,EAAAA,EAAAA,IAkCM,MAlCNpB,EAkCM,CAhCIwD,EAAMC,IAAIiB,SAAWxE,EAAA0E,iBAAiBC,SAAWrB,EAAMC,IAAIiB,SAAWxE,EAAA0E,iBAAiBE,QD4JxF5D,EAAAA,EAAAA,OC7JP+C,EAAAA,EAAAA,IAOYvD,EAAA,CDuJJvB,IAAK,EC5JXoD,KAAK,UACLa,KAAK,QACJpB,QAAKR,GAAEtB,EAAA6E,cAAcvB,EAAMC,IAAIuB,KD8JzB,CACD9C,SAASC,EAAAA,EAAAA,IC9JhB,IAEDhC,EAAA,KAAAA,EAAA,KD6JUiC,EAAAA,EAAAA,IC/JT,aDiKOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVW,EAAAA,EAAAA,IAAoB,IAAI,GC/JtBO,EAAMC,IAAIiB,SAAWxE,EAAA0E,iBAAiBK,SDiKvC/D,EAAAA,EAAAA,OClKP+C,EAAAA,EAAAA,IAOYvD,EAAA,CD4JJvB,IAAK,ECjKXoD,KAAK,UACLa,KAAK,QACJpB,QAAKR,GAAEtB,EAAAgF,oBAAoB1B,EAAMC,MDmK3B,CACDvB,SAASC,EAAAA,EAAAA,ICnKhB,IAEDhC,EAAA,MAAAA,EAAA,MDkKUiC,EAAAA,EAAAA,ICpKT,aDsKOC,EAAG,EACHC,GAAI,CAAC,KACJ,KAAM,CAAC,cACVW,EAAAA,EAAAA,IAAoB,IAAI,ICrK9B5B,EAAAA,EAAAA,IAMYX,EAAA,CALV6B,KAAK,UACLa,KAAK,QACJpB,QAAKR,GAAEtB,EAAAiF,mBAAmB3B,EAAMC,IAAIc,ODuKlC,CACDrC,SAASC,EAAAA,EAAAA,ICvKZ,IAEDhC,EAAA,MAAAA,EAAA,MDsKMiC,EAAAA,EAAAA,ICxKL,cD0KGC,EAAG,EACHC,GAAI,CAAC,KACJ,KAAM,CAAC,aCxKZjB,EAAAA,EAAAA,IAMYX,EAAA,CALV6B,KAAK,SACLa,KAAK,QACJpB,QAAKR,GAAEtB,EAAAkF,eAAe5B,EAAMC,IAAIuB,KD0K9B,CACD9C,SAASC,EAAAA,EAAAA,IC1KZ,IAEDhC,EAAA,MAAAA,EAAA,MDyKMiC,EAAAA,EAAAA,IC3KL,WD6KGC,EAAG,EACHC,GAAI,CAAC,KACJ,KAAM,CAAC,gBAGdD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,gBC1KbhB,EAAAA,EAAAA,IAKEJ,EAAA,CD0KAK,WC9KSpB,EAAAmF,mBD+KT,sBAAuBlF,EAAO,KAAOA,EAAO,GAAMqB,GC/KzCtB,EAAAmF,mBAAkB7D,GAC1B,cAAatB,EAAAoF,iBACb,gBAAepF,EAAAqF,oBACfC,cAActF,EAAAuF,mBDgLd,KAAM,EAAG,CAAC,aAAc,cAAe,gBAAiB,mBAE/D,C,4JE5UA,MAAM7G,EAAa,CAAEC,MAAO,kBACtBC,EAAa,CAAED,MAAO,kBACtBE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAEH,MAAO,eACtBI,EAAa,CAAEE,IAAK,GACpBD,EAAa,CACjBC,IAAK,EACLN,MAAO,iBAEHO,EAAa,CAAEP,MAAO,iBAe5B,OAA4B6G,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACLtE,WAAY,CAAEiB,KAAMsD,SACpBC,WAAY,CAAC,EACbC,aAAc,CAAC,GAEjBC,MAAO,CAAC,oBAAqB,gBAC7BC,KAAAA,CAAMC,GAAgBC,KAAMC,IC6F9B,MAAMR,EAAQM,EAGRC,EAAOC,EAMPC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAaD,EAAAA,EAAAA,IAAI,IACjBE,GAAcF,EAAAA,EAAAA,IAAI,IAClBG,GAAiBH,EAAAA,EAAAA,IAAmB,IACpCI,GAAoBJ,EAAAA,EAAAA,KAAI,GACxBK,GAAYL,EAAAA,EAAAA,IAAI,IAGhBM,GAAUC,EAAAA,EAAAA,IAAS,KACvB,MAAMC,EAAO,IAAIC,IAAInB,EAAMG,aAAaiB,IAAIC,GAAKA,EAAEC,UACnD,OAAOC,MAAMC,KAAKN,GAAMO,SAGpBC,GAAiBT,EAAAA,EAAAA,IAAS,KAC9B,IAAIU,EAAS3B,EAAMG,aAQnB,GALIS,EAAY9C,QACd6D,EAASA,EAAOC,OAAOP,GAAKA,EAAEC,UAAYV,EAAY9C,QAIpD6C,EAAW7C,MAAO,CACpB,MAAM+D,EAASlB,EAAW7C,MAAMgE,cAChCH,EAASA,EAAOC,OAAOP,GACrBA,EAAErD,KAAK8D,cAAcC,SAASF,IAC9BR,EAAEW,YAAYF,cAAcC,SAASF,G,CAIzC,OAAOF,KAITM,EAAAA,EAAAA,IAAM,IAAMjC,EAAMtE,WAAawG,IAC7BzB,EAAQ3C,MAAQoE,EACZA,IAEFvB,EAAW7C,MAAQ,GACnB+C,EAAe/C,MAAQ,GAEnBkD,EAAQlD,MAAMd,OAAS,IACzB4D,EAAY9C,MAAQkD,EAAQlD,MAAM,GAElCqE,SAKNF,EAAAA,EAAAA,IAAMxB,EAAUyB,IACd3B,EAAK,oBAAqB2B,KAI5B,MAAME,EAAkBA,KAEtBD,KAGIA,EAA4BA,KAChCtB,EAAe/C,MAAQ,IAAI4D,EAAe5D,QAGtCuE,EAAcA,KAClB5B,EAAQ3C,OAAQ,GAGZwE,EAAyBC,IAC7B1B,EAAe/C,MAAQyE,GAYnBC,EAAuBC,IAC3B,OAAQA,GACN,KAAKC,EAAAA,GAAUC,OAAQ,MAAO,GAC9B,KAAKD,EAAAA,GAAUE,QAAS,MAAO,UAC/B,KAAKF,EAAAA,GAAUG,OAAQ,MAAO,UAC9B,KAAKH,EAAAA,GAAUzC,QAAS,MAAO,UAC/B,KAAKyC,EAAAA,GAAUI,KAAM,MAAO,UAC5B,KAAKJ,EAAAA,GAAUnB,MAAO,MAAO,OAC7B,KAAKmB,EAAAA,GAAUK,OAAQ,MAAO,OAC9B,QAAS,MAAO,KAIdC,EAAelF,GACL,OAAVA,QAA4BmF,IAAVnF,EACb,GAGY,kBAAVA,EACFA,EAAMd,OAAS,GAAKc,EAAMoF,UAAU,EAAG,IAAM,MAAQpF,EAGvD6E,OAAO7E,GAGVqF,EAAiBC,IACrB,IACE,GAA2B,kBAAhBA,EAAMtF,MAAoB,CAEnC,MAAMuF,EAASC,KAAKC,MAAMH,EAAMtF,OAChCiD,EAAUjD,MAAQwF,KAAKE,UAAUH,EAAQ,KAAM,E,MAG/CtC,EAAUjD,MAAQwF,KAAKE,UAAUJ,EAAMtF,MAAO,KAAM,E,CAEtD,MAEAiD,EAAUjD,MAAQ6E,OAAOS,EAAMtF,M,CAGjCgD,EAAkBhD,OAAQ,GAGtB2F,EAAsBA,KACU,IAAhC5C,EAAe/C,MAAMd,QAKzBuD,EAAK,eAAgBM,EAAe/C,OACpC4F,EAAAA,GAAUC,QAAQ,QAAQ9C,EAAe/C,MAAMd,cAC/CqF,KANEqB,EAAAA,GAAUE,QAAQ,eDrFtB,MAAO,CAACtJ,EAAUC,KAChB,MAAMsJ,GAAuBhJ,EAAAA,EAAAA,IAAkB,aACzCiJ,GAAuBjJ,EAAAA,EAAAA,IAAkB,aACzCK,GAAqBL,EAAAA,EAAAA,IAAkB,WACvCE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAA6BH,EAAAA,EAAAA,IAAkB,mBAC/CO,GAAoBP,EAAAA,EAAAA,IAAkB,UACtCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCM,GAAsBN,EAAAA,EAAAA,IAAkB,YACxCkJ,GAAsBlJ,EAAAA,EAAAA,IAAkB,YACxCmJ,GAAuBnJ,EAAAA,EAAAA,IAAkB,aAE/C,OAAQS,EAAAA,EAAAA,OC5LR+C,EAAAA,EAAAA,IA+GY2F,EAAA,CD8EVtI,WC5LS+E,EAAA3C,MD6LT,sBAAuBvD,EAAO,KAAOA,EAAO,GAAMqB,GC7LzC6E,EAAO3C,MAAAlC,GAChB8C,MAAM,SACNf,MAAM,MACL,eAAc0E,EACf,uBD8LC,CChHU4B,QAAM1H,EAAAA,EAAAA,IACf,IASM,EATNf,EAAAA,EAAAA,IASM,MATNhC,EASM,EARJiC,EAAAA,EAAAA,IAA8CX,EAAA,CAAlCsB,QAAOiG,GAAW,CDkH5B/F,SAASC,EAAAA,EAAAA,IClHqB,IAAEhC,EAAA,KAAAA,EAAA,KDmH9BiC,EAAAA,EAAAA,ICnH4B,SDqH9BC,EAAG,EACHC,GAAI,CAAC,MCrHPjB,EAAAA,EAAAA,IAMYX,EAAA,CALV6B,KAAK,UACJP,QAAOqH,EACPS,SAAoC,IAA1BrD,EAAA/C,MAAed,QDwHzB,CACDV,SAASC,EAAAA,EAAAA,ICxHV,IACO,EDwHJC,EAAAA,EAAAA,ICzHH,WACOU,EAAAA,EAAAA,IAAG2D,EAAA/C,MAAed,QAAS,KACnC,KDyHEP,EAAG,GACF,EAAG,CAAC,iBAGXH,SAASC,EAAAA,EAAAA,IClNT,IA0EM,EA1ENf,EAAAA,EAAAA,IA0EM,MA1ENxC,EA0EM,EAxEJwC,EAAAA,EAAAA,IAyBM,MAzBNtC,EAyBM,EAxBJuC,EAAAA,EAAAA,IAYYqI,EAAA,CDuMRpI,WClNOkF,EAAA9C,MDmNP,sBAAuBvD,EAAO,KAAOA,EAAO,GAAMqB,GCnN3CgF,EAAW9C,MAAAlC,GACpBG,YAAY,SACZC,MAAA,gBACCC,SAAQmG,GDoNN,CACD9F,SAASC,EAAAA,EAAAA,IClNT,IAAsB,GDmNnBjB,EAAAA,EAAAA,KAAW,ICpNhBC,EAAAA,EAAAA,IAKE4I,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJcpD,EAAAlD,MAAPuG,KDoNK/I,EAAAA,EAAAA,OCrNd+C,EAAAA,EAAAA,IAKEwF,EAAA,CAHCtK,IAAK8K,EACL3G,MAAO2G,EACPvG,MAAOuG,GDqND,KAAM,EAAG,CAAC,QAAS,YACpB,QAEN5H,EAAG,GACF,EAAG,CAAC,gBCrNThB,EAAAA,EAAAA,IASWV,EAAA,CD8MPW,WCtNOiF,EAAA7C,MDuNP,sBAAuBvD,EAAO,KAAOA,EAAO,GAAMqB,GCvN3C+E,EAAU7C,MAAAlC,GACnBG,YAAY,YACZI,UAAA,GACAH,MAAA,iBDwNG,CCtNQsI,QAAM/H,EAAAA,EAAAA,IACf,IAA6B,EAA7Bd,EAAAA,EAAAA,IAA6BP,EAAA,MDwNzBoB,SAASC,EAAAA,EAAAA,ICxNJ,IAAU,EAAVd,EAAAA,EAAAA,KAAU8I,EAAAA,EAAAA,IAAAC,EAAAA,WD2Nf/H,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,kBCzNXjB,EAAAA,EAAAA,IAiCM,MAjCNrC,EAiCM,EAhCJsC,EAAAA,EAAAA,IA+BWN,EAAA,CA9BRmC,KAAMoE,EAAA5D,MACP2G,OAAO,MACNC,kBAAkBpC,EACnB,UAAQ,QD4NL,CACDhG,SAASC,EAAAA,EAAAA,IC3NX,IAA+C,EAA/Cd,EAAAA,EAAAA,IAA+CT,EAAA,CAA9B2B,KAAK,YAAYgB,MAAM,QACxClC,EAAAA,EAAAA,IAA6ET,EAAA,CAA5DyC,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDlC,EAAAA,EAAAA,IAMkBT,EAAA,CANDyC,KAAK,YAAYC,MAAM,KAAKC,MAAM,ODwO5C,CCvOMrB,SAAOC,EAAAA,EAAAA,IAChB,EADoBsB,SAAG,EACvBpC,EAAAA,EAAAA,IAESL,EAAA,CAFAuB,KAAM6F,EAAoB3E,EAAI4E,WAAYjF,KAAK,SD2OjD,CACDlB,SAASC,EAAAA,EAAAA,IC3Ob,IAAmB,ED4ObC,EAAAA,EAAAA,KAAiBU,EAAAA,EAAAA,IC5OpBW,EAAI4E,WAAS,KD8OZhG,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KC7OThB,EAAAA,EAAAA,IAckBT,EAAA,CAdDyC,KAAK,QAAQC,MAAM,IAAI,YAAU,MAAM,4BDoPjD,CCnPMpB,SAAOC,EAAAA,EAAAA,IAChB,EADoBsB,SAAG,EACvBrC,EAAAA,EAAAA,IAUM,MAVNpC,EAUM,CAT0B,SAAlByE,EAAI4E,YDqPPnH,EAAAA,EAAAA,OCrPTC,EAAAA,EAAAA,IAAyE,OAAAlC,GAAA6D,EAAAA,EAAAA,IAAhC8F,EAAYnF,EAAIC,QAAK,MDsPrDxC,EAAAA,EAAAA,OCrPT+C,EAAAA,EAAAA,IAOYvD,EAAA,CD+OFvB,IAAK,ECpPboD,KAAK,OACLa,KAAK,QACJpB,QAAKR,GAAEuH,EAActF,IDsPb,CACDvB,SAASC,EAAAA,EAAAA,ICtPlB,IAEDhC,EAAA,KAAAA,EAAA,KDqPYiC,EAAAA,EAAAA,ICvPX,eDyPSC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,iBAGlBD,EAAG,KCxPThB,EAAAA,EAAAA,IAAuFT,EAAA,CAAtEyC,KAAK,cAAcC,MAAM,KAAK,YAAU,MAAM,+BDiQ7DjB,EAAG,GACF,EAAG,CAAC,WC7PsBoE,EAAA/C,MAAed,OAAS,IDgQlD1B,EAAAA,EAAAA,OChQPC,EAAAA,EAAAA,IAOM,MAPNjC,EAOM,EANJmC,EAAAA,EAAAA,IAKEsI,EAAA,CAJCrF,MAAK,OAASmC,EAAA/C,MAAed,aAC9BL,KAAK,OACJgI,UAAU,EACX,gBDiQO,KAAM,EAAG,CAAC,cAEftH,EAAAA,EAAAA,IAAoB,IAAI,MChPhC5B,EAAAA,EAAAA,IAaYuI,EAAA,CDsORtI,WClPOoF,EAAAhD,MDmPP,sBAAuBvD,EAAO,KAAOA,EAAO,GAAMqB,GCnP3CkF,EAAiBhD,MAAAlC,GAC1B8C,MAAM,WACNf,MAAM,MACN,qBDoPG,CACDrB,SAASC,EAAAA,EAAAA,ICnPX,IAME,EANFd,EAAAA,EAAAA,IAMEV,EAAA,CD+OIW,WCpPKqF,EAAAjD,MDqPL,sBAAuBvD,EAAO,KAAOA,EAAO,GAAMqB,GCrP7CmF,EAASjD,MAAAlC,GAClBe,KAAK,WACJiI,KAAM,GACPC,SAAA,GACA7I,MAAA,4CDsPK,KAAM,EAAG,CAAC,iBAEfS,EAAG,GACF,EAAG,CAAC,iBAETA,EAAG,GACF,EAAG,CAAC,eAET,I,UErWA,MAAMqI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QH2KA,GAAeC,EAAAA,EAAAA,IAAgB,CAC7B/G,KAAM,oBACNgH,WAAY,CACVC,iBAAgB,EAChBC,SAAQA,EAAAA,UAEV7E,KAAAA,GAEE,MAAM8E,GAAYzE,EAAAA,EAAAA,IAAmB,IAC/B0E,GAAa1E,EAAAA,EAAAA,IAAY,YACzB2E,GAAmB3E,EAAAA,EAAAA,IAAY,IAC/B4E,GAAqB5E,EAAAA,EAAAA,IAAY,IACjC/E,GAAuB+E,EAAAA,EAAAA,IAAc,IACrC6E,GAAW7E,EAAAA,EAAAA,IAAY,IACvB3D,GAAY2D,EAAAA,EAAAA,IAAoB,IAChC3C,GAAkB2C,EAAAA,EAAAA,IAA+B,CAAC,GAClD8E,GAAiB9E,EAAAA,EAAAA,IAAY,IAC7BzD,GAAgByD,EAAAA,EAAAA,IAAY,IAG5B+E,GAAU/E,EAAAA,EAAAA,KAAI,GACd5D,GAAa4D,EAAAA,EAAAA,KAAI,GAGjBjB,GAAqBiB,EAAAA,EAAAA,KAAI,GACzBhB,GAAmBgB,EAAAA,EAAAA,IAAuB,MAC1Cf,GAAsBe,EAAAA,EAAAA,IAAmB,IACzCgF,GAAmBhF,EAAAA,EAAAA,IAA+B,CAAC,GAGnDtC,GAAgBsC,EAAAA,EAAAA,IAAkB,IAClCiF,GAAiBjF,EAAAA,EAAAA,IAAiB,IAAIS,KAGtCyE,GAAa3E,EAAAA,EAAAA,IAAS,KAC1B,MAAM4E,EAAc,IAAI1E,IAAIgE,EAAUrH,MAAMsD,IAAI0E,GAAKA,EAAEC,WACvD,OAAOxE,MAAMC,KAAKqE,GAAajE,OAAOoE,GAAKA,KAGvClK,GAAkBmF,EAAAA,EAAAA,IAAS,IACxB2E,EAAW9H,MAAMsD,IAAI2E,IAAO,CACjCjI,MAAOiI,EACPrI,MAAOqI,EACPE,SAAUd,EAAUrH,MACjB8D,OAAOkE,GAAKA,EAAEC,WAAaA,GAC3B3E,IAAI8E,IAAO,CACVpI,MAAOoI,EAAS9G,GAChB1B,MAAOwI,EAASlI,YAKlBmI,GAAoBlF,EAAAA,EAAAA,IAAS,IAC5BoE,EAAiBvH,MACfqH,EAAUrH,MAAM8D,OAAOkE,GAAKA,EAAEC,WAAaV,EAAiBvH,OAD/B,IAIhCX,GAAoB8D,EAAAA,EAAAA,IAAS,KACjC,IAAKhE,EAAca,MAAO,OAAOf,EAAUe,MAC3C,MAAMsI,EAAUnJ,EAAca,MAAMgE,cACpC,OAAO/E,EAAUe,MAAM8D,OAAOyE,GAC5BA,EAAErI,KAAK8D,cAAcC,SAASqE,IAC9BC,EAAE1J,KAAKmF,cAAcC,SAASqE,IAC7BC,EAAEvI,OAASuI,EAAEvI,MAAMgE,cAAcC,SAASqE,MAIzChJ,GAAqB6D,EAAAA,EAAAA,IAAS,IAAM7C,EAAcN,MAAMd,QAExDsJ,GAAsBrF,EAAAA,EAAAA,IAAS,IAAM0E,EAAe7H,MAAMN,MAE1D+I,GAAoBtF,EAAAA,EAAAA,IAAS,IAAM0E,EAAe7H,MAAMN,KAAO,GAG/DgJ,EAAgBC,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaC,eAC3C1B,EAAUrH,MAAQ4I,EAASpJ,KAGvBsI,EAAW9H,MAAMd,OAAS,IAC5BqI,EAAiBvH,MAAQ8H,EAAW9H,MAAM,GAC1CgJ,I,CAEF,MAAOC,GACPC,QAAQD,MAAM,UAAWA,E,GAIvBE,EAAqBA,KAEzBlK,EAAUe,MAAQ,GAClBC,EAAgBD,MAAQ,CAAC,EACzB0H,EAAe1H,MAAQ,GACvB6H,EAAe7H,MAAMoJ,SAGjBJ,EAAmBA,KACnBX,EAAkBrI,MAAMd,OAAS,EACnCsI,EAAmBxH,MAAQqI,EAAkBrI,MAAM,GAAGsB,GAEtDkG,EAAmBxH,MAAQ,IAIzBzB,EAAkBoK,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaO,aAC3C5B,EAASzH,MAAQ4I,EAASpJ,WAEpB8J,G,CACN,MAAOL,GACPC,QAAQD,MAAM,UAAWA,E,GAIvBK,EAAoBX,UACxB,GAAKlB,EAASzH,MAAd,CAEA2H,EAAQ3H,OAAQ,EAChB,IACE,MAAMuJ,EAAoC,CACxCjC,WAAY,OACZG,SAAUA,EAASzH,OAGf4I,QAAiBC,EAAAA,GAAOC,aAAaU,UAAUD,GAC/CE,EAASb,EAASpJ,KAExBP,EAAUe,MAAQyJ,EAAOxK,UACzByI,EAAe1H,MAAQyJ,EAAO/B,eAG9BzH,EAAgBD,MAAQ,CAAC,EACzByJ,EAAOxK,UAAUyK,QAAQC,IACvB1J,EAAgBD,MAAM2J,EAASzJ,MAAQyJ,EAAS3J,OAAS,KAG3D6H,EAAe7H,MAAMoJ,QACrBxD,EAAAA,GAAUC,QAAQ,QAAQ4D,EAAOxK,UAAUC,a,CAC3C,MAAO+J,GACPC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,S,CAClB,QACEtB,EAAQ3H,OAAQ,C,CA3BS,GA+BvB5B,EAAwB4B,IACxBA,GAA0B,IAAjBA,EAAMd,SACjBqI,EAAiBvH,MAAQA,EAAM,GAC/BwH,EAAmBxH,MAAQA,EAAM,GACjC4J,MAIEC,EAA0B5B,GACvBZ,EAAUrH,MAAM8D,OAAOkE,GAAKA,EAAEC,WAAaA,GAG9C2B,EAAuBjB,UAC3B,GAAKnB,EAAmBxH,MAAxB,CAEA2H,EAAQ3H,OAAQ,EAChB,IACE,MAAMuJ,EAAoC,CACxCjC,WAAY,WACZwC,WAAYtC,EAAmBxH,MAC/ByH,SAAU,IAGNmB,QAAiBC,EAAAA,GAAOC,aAAaU,UAAUD,GAC/CE,EAASb,EAASpJ,KAExBP,EAAUe,MAAQyJ,EAAOxK,UACzByI,EAAe1H,MAAQyJ,EAAO/B,eAG9BzH,EAAgBD,MAAQ,CAAC,EACzByJ,EAAOxK,UAAUyK,QAAQC,IACvB1J,EAAgBD,MAAM2J,EAASzJ,MAAQyJ,EAAS3J,OAAS,KAG3D6H,EAAe7H,MAAMoJ,QACrBxD,EAAAA,GAAUC,QAAQ,UAAU4D,EAAOxK,UAAUC,a,CAC7C,MAAO+J,GACPC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,S,CAClB,QACEtB,EAAQ3H,OAAQ,C,CA5BmB,GAgCjCG,EAAqB4J,IACzBlC,EAAe7H,MAAMgK,IAAID,IAGrBjL,EAAgB6J,UACpB3J,EAAWgB,OAAQ,EACnB,IACE,MAAMuJ,EAA+B,CACnCjC,WAAYA,EAAWtH,MACvB8J,WAAiC,aAArBxC,EAAWtH,MAAuBwH,EAAmBxH,WAAQmF,EACzEsC,SAA+B,SAArBH,EAAWtH,MAAmByH,EAASzH,MAAQ,GACzDC,gBAAiBA,EAAgBD,OAG7B4I,QAAiBC,EAAAA,GAAOC,aAAamB,YAAYV,GACjDE,EAASb,EAASpJ,KAExBoG,EAAAA,GAAUC,QAAQ,WAClBgC,EAAe7H,MAAMoJ,cAEfP,EAAAA,GAAOqB,SAASC,aAAaV,EAAOW,e,CAE1C,MAAOnB,GACPC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,S,CAClB,QACEjK,EAAWgB,OAAQ,C,GAKjBqK,EAAoBA,CAACC,EAAkBzG,KAC3CjC,EAAiB5B,MAAQsK,EACzBzI,EAAoB7B,MAAQ6D,EAC5BlC,EAAmB3B,OAAQ,GAGvB+B,EAAqB8B,IAezB,GAdAA,EAAO6F,QAAQpE,IAEb,MAAMqE,EAAW1K,EAAUe,MAAMuK,KAAKhC,GAAKA,EAAErI,KAAK8D,gBAAkBsB,EAAMpF,KAAK8D,eAC3E2F,IAEF1J,EAAgBD,MAAM2J,EAASzJ,MAAQ2E,OAAOS,EAAMtF,OAEpD4H,EAAiB5H,MAAM2J,EAASzJ,MAAQoF,EAAMkF,OAE9C3C,EAAe7H,MAAMgK,IAAIL,EAASzJ,SAKlC0B,EAAiB5B,MAAO,CAC1B,MAAMyK,EAAgBnK,EAAcN,MAAM0K,UAAUC,GAAKA,EAAErJ,KAAOM,EAAiB5B,MAAOsB,IACtFmJ,GAAiB,EACnBnK,EAAcN,MAAMyK,GAAiB7I,EAAiB5B,MAEtDM,EAAcN,MAAM4K,KAAKhJ,EAAiB5B,M,CAI9C4F,EAAAA,GAAUC,QAAQ,QAAQhC,EAAO3E,eAG7BkB,EAAsB2J,GACnBnC,EAAiB5H,MAAM+J,GAG1Bc,EAAehK,GACdA,GACEA,EAAKiK,MAAM,SAASC,OADT,GAIdtK,EAAsBC,IAC1B,OAAQA,GACN,KAAKsK,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdC,EAAyBd,IAE7B,IAAIe,EAAQ,EAMZ,OALApG,OAAOqG,KAAK1D,EAAiB5H,OAAO0J,QAAQK,IACtCnC,EAAiB5H,MAAM+J,KAAeO,EAAKxJ,UAC7CuK,MAGGA,GAGHE,EAAqB5C,UACzB,UAEQE,EAAAA,GAAOC,aAAa0C,iBAAiB,CAAEC,WAG7C,MAAMC,EAAYpL,EAAcN,MAAM0K,UAAUC,GAAKA,EAAErJ,KAAOmK,GAC1DC,GAAa,IACfpL,EAAcN,MAAM2L,OAAOD,EAAW,GACtC9F,EAAAA,GAAUC,QAAQ,W,CAEpB,MAAOoD,GACPC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,S,GAKd5I,EAAkBsI,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAa8C,oBACrCC,EAAYjD,EAASpJ,KAEvBqM,GAAaA,EAAU3M,OAAS,UAC5B4M,EAAaD,GACnBjG,EAAAA,GAAUC,QAAQ,QAAQgG,EAAU3M,kB,CAEtC,MAAO+J,GACPC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,S,GAId6C,EAAenD,UACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaiD,eAAe,CAAEF,cACtDG,EAAWpD,EAASpJ,KAY1B,OATAwM,EAAStC,QAAQY,IACf,MAAMG,EAAgBnK,EAAcN,MAAM0K,UAAUC,GAAKA,EAAErJ,KAAOgJ,EAAKhJ,IACnEmJ,GAAiB,EACnBnK,EAAcN,MAAMyK,GAAiBH,EAErChK,EAAcN,MAAM4K,KAAKN,KAItB0B,C,CACP,MAAO/C,GAEP,MADAC,QAAQD,MAAM,UAAWA,GACnBA,C,GAIJ5H,EAAgBsH,UACpB,IAEE,MAAM2B,EAAOhK,EAAcN,MAAMuK,KAAKI,GAAKA,EAAErJ,KAAOmK,GAChDnB,IACFA,EAAKtJ,OAASE,EAAAA,GAAiB+K,SAGjC,MAAMrD,QAAiBC,EAAAA,GAAOC,aAAaoD,gBAAgB,CACzDT,WAGIU,EAAcvD,EAASpJ,KAGvB4M,EAAQ9L,EAAcN,MAAM0K,UAAUC,GAAKA,EAAErJ,KAAOmK,GACtDW,GAAS,IACX9L,EAAcN,MAAMoM,GAASD,GAG/BvG,EAAAA,GAAUC,QAAQ,eAAesG,EAAY9J,cAAcnD,QAAU,SAGjEiN,EAAY9J,cAAgB8J,EAAY9J,aAAanD,OAAS,IAChE0C,EAAiB5B,MAAQmM,EACzBtK,EAAoB7B,MAAQmM,EAAY9J,aACxCV,EAAmB3B,OAAQ,E,CAE7B,MAAOiJ,GACPC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,UAGhB,MAAMqB,EAAOhK,EAAcN,MAAMuK,KAAKI,GAAKA,EAAErJ,KAAOmK,GAChDnB,IACFA,EAAKtJ,OAASE,EAAAA,GAAiBE,M,GAK/BI,EAAuB8I,IACvBA,EAAKjI,cAAgBiI,EAAKjI,aAAanD,OAAS,GAClD0C,EAAiB5B,MAAQsK,EACzBzI,EAAoB7B,MAAQsK,EAAKjI,aACjCV,EAAmB3B,OAAQ,GAE3B4F,EAAAA,GAAUE,QAAQ,cAIhBrE,EAAqBkH,UACzB,UACQE,EAAAA,GAAOqB,SAASC,aAAa1C,E,CACnC,MAAOwB,GACPC,QAAQD,MAAM,WAAYA,GAC1BrD,EAAAA,GAAUqD,MAAM,U,GAIdvH,EAAkB+J,IACtBF,EAAmBE,IAGfjL,GAAoBmI,UACxB,UACQ0D,EAAAA,EAAaC,QAAQ,kBAAmB,KAAM,CAClDC,kBAAmB,KACnBC,iBAAkB,KAClB3N,KAAM,kBAIFgK,EAAAA,GAAOC,aAAa2D,mBAG1BnM,EAAcN,MAAQ,GACtB4F,EAAAA,GAAUC,QAAQ,c,CAClB,MAAOoD,GACO,WAAVA,IACFC,QAAQD,MAAM,UAAWA,GACzBrD,EAAAA,GAAUqD,MAAM,U,GAKhBlI,GAAoBC,IACxB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,GACtC,KAAKD,EAAAA,GAAiB+K,QAAS,MAAO,UACtC,KAAK/K,EAAAA,GAAiBK,OAAQ,MAAO,UACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,SACpC,QAAS,MAAO,KAIdH,GAAiBD,IACrB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,MACtC,KAAKD,EAAAA,GAAiB+K,QAAS,MAAO,MACtC,KAAK/K,EAAAA,GAAiBK,OAAQ,MAAO,MACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,OACpC,QAAS,OAAOJ,IAKd0L,GAAgB/D,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAa6D,iBAC3CrM,EAAcN,MAAQ4I,EAASpJ,I,CAC/B,MAAOyJ,GACPC,QAAQD,MAAM,YAAaA,GAC3BrD,EAAAA,GAAUqD,MAAM,W,GASpB,OALA2D,EAAAA,EAAAA,IAAUjE,gBACFD,UACAgE,OAGD,CACLrF,YACAC,aACAC,mBACAC,qBACA3J,uBACA4J,WACAxI,YACAgB,kBACAyH,iBACAvI,gBACAwI,UACA3I,aACA8I,aACA9J,kBACAqK,oBACAhJ,oBACAC,qBACAkJ,sBACAC,oBACAnI,gBACA6I,qBACAH,mBACAzK,kBACAH,uBACAyL,yBACAD,uBACAzJ,oBACArB,gBACA+L,cACApK,qBACA2K,wBACAG,qBAEAlL,kBACAgB,gBACAG,sBACAC,qBACAC,iBACAlB,qBACAO,oBACAE,iBAEAU,qBACAC,mBACAC,sBACAwI,oBACAtI,oBACA3B,qBAEAgH,SAAQ,WACRlG,iBAAgBA,EAAAA,GAEpB,II/qBF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS3E,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/ParameterToolView.vue?ac0d", "webpack://tab-kit-web/./src/views/ParameterToolView.vue", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?64b6", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?7eb6", "webpack://tab-kit-web/./src/views/ParameterToolView.vue?3f0a"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, createCommentVNode as _createCommentVNode, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"parameter-tool\" }\nconst _hoisted_2 = { class: \"main-content\" }\nconst _hoisted_3 = { class: \"cin-operations-section\" }\nconst _hoisted_4 = { class: \"cin-operations-content\" }\nconst _hoisted_5 = { class: \"operation-row\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"search-bar\"\n}\nconst _hoisted_7 = { class: \"filter-info\" }\nconst _hoisted_8 = {\n  key: 0,\n  class: \"import-info\"\n}\nconst _hoisted_9 = { class: \"parameters-section\" }\nconst _hoisted_10 = { class: \"current-value\" }\nconst _hoisted_11 = {\n  key: 0,\n  class: \"parameter-source\"\n}\nconst _hoisted_12 = {\n  key: 1,\n  class: \"no-source\"\n}\nconst _hoisted_13 = { class: \"data-files-section\" }\nconst _hoisted_14 = { class: \"section-header\" }\nconst _hoisted_15 = { class: \"header-actions\" }\nconst _hoisted_16 = { class: \"data-files-table\" }\nconst _hoisted_17 = [\"title\"]\nconst _hoisted_18 = { class: \"name\" }\nconst _hoisted_19 = { class: \"action-buttons\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_cascader = _resolveComponent(\"el-cascader\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_document = _resolveComponent(\"document\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_ParamParseDialog = _resolveComponent(\"ParamParseDialog\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"section-header\" }, [\n          _createElementVNode(\"h4\", null, \"CIN 文件操作\")\n        ], -1)),\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createElementVNode(\"div\", _hoisted_5, [\n            _createVNode(_component_el_cascader, {\n              modelValue: _ctx.selectedTemplatePath,\n              \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.selectedTemplatePath) = $event)),\n              options: _ctx.cascaderOptions,\n              placeholder: \"选择 CIN 模板\",\n              style: {\"width\":\"300px\"},\n              onChange: _ctx.handleTemplateChange,\n              clearable: \"\"\n            }, null, 8, [\"modelValue\", \"options\", \"onChange\"]),\n            _createVNode(_component_el_button, {\n              onClick: _ctx.selectLocalFile,\n              style: {\"margin-left\":\"16px\"}\n            }, {\n              default: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createTextVNode(\"选择本地 CIN 文件\")\n              ])),\n              _: 1,\n              __: [3]\n            }, 8, [\"onClick\"]),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              onClick: _ctx.exportCinFile,\n              loading: _ctx.processing,\n              style: {\"margin-left\":\"16px\"}\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [\n                _createTextVNode(\"导出CIN\")\n              ])),\n              _: 1,\n              __: [4]\n            }, 8, [\"onClick\", \"loading\"])\n          ])\n        ])\n      ]),\n      (_ctx.variables.length > 0)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n            _createVNode(_component_el_input, {\n              modelValue: _ctx.searchKeyword,\n              \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.searchKeyword) = $event)),\n              placeholder: \"搜索参数...\",\n              clearable: \"\",\n              style: {\"width\":\"300px\"}\n            }, null, 8, [\"modelValue\"]),\n            _createElementVNode(\"div\", _hoisted_7, [\n              _createElementVNode(\"span\", null, \"显示 \" + _toDisplayString(_ctx.filteredVariables.length) + \" / \" + _toDisplayString(_ctx.variables.length) + \" 个参数\", 1),\n              (_ctx.importedFilesCount > 0)\n                ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, \" 已导入 \" + _toDisplayString(_ctx.importedFilesCount) + \" 个文件的参数 \", 1))\n                : _createCommentVNode(\"\", true)\n            ])\n          ]))\n        : _createCommentVNode(\"\", true),\n      _createElementVNode(\"div\", _hoisted_9, [\n        _createVNode(_component_el_table, {\n          data: _ctx.filteredVariables,\n          border: \"\",\n          style: {\"width\":\"100%\",\"height\":\"100%\"},\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_table_column, {\n              prop: \"name\",\n              label: \"参数名\",\n              width: \"200\",\n              \"show-overflow-tooltip\": \"\"\n            }),\n            _createVNode(_component_el_table_column, {\n              prop: \"type\",\n              label: \"类型\",\n              width: \"120\",\n              \"show-overflow-tooltip\": \"\"\n            }),\n            _createVNode(_component_el_table_column, {\n              prop: \"value\",\n              label: \"当前值\",\n              width: \"200\",\n              \"show-overflow-tooltip\": \"\"\n            }, {\n              default: _withCtx((scope) => [\n                _createElementVNode(\"span\", _hoisted_10, _toDisplayString(scope.row.value || '(空)'), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"新值\",\n              \"min-width\": \"200\"\n            }, {\n              default: _withCtx((scope) => [\n                _createVNode(_component_el_input, {\n                  modelValue: _ctx.parameterValues[scope.row.name],\n                  \"onUpdate:modelValue\": ($event: any) => ((_ctx.parameterValues[scope.row.name]) = $event),\n                  placeholder: \"输入新值\",\n                  size: \"small\",\n                  onChange: ($event: any) => (_ctx.onParameterChange(scope.row.name))\n                }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"来源\",\n              width: \"150\",\n              \"show-overflow-tooltip\": \"\"\n            }, {\n              default: _withCtx((scope) => [\n                (_ctx.getParameterSource(scope.row.name))\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_document)\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.getParameterSource(scope.row.name)), 1)\n                    ]))\n                  : (_openBlock(), _createElementBlock(\"span\", _hoisted_12, \"CIN 文件\"))\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }, 8, [\"data\"])\n      ]),\n      _createElementVNode(\"div\", _hoisted_13, [\n        _createElementVNode(\"div\", _hoisted_14, [\n          _cache[8] || (_cache[8] = _createElementVNode(\"h4\", null, \"数据文件 (ARXML/SDDB/LDF)\", -1)),\n          _createElementVNode(\"div\", _hoisted_15, [\n            _createVNode(_component_el_button, {\n              size: \"small\",\n              type: \"primary\",\n              onClick: _ctx.selectDataFiles\n            }, {\n              default: _withCtx(() => _cache[6] || (_cache[6] = [\n                _createTextVNode(\"添加文件\")\n              ])),\n              _: 1,\n              __: [6]\n            }, 8, [\"onClick\"]),\n            (_ctx.importedFiles.length > 0)\n              ? (_openBlock(), _createBlock(_component_el_button, {\n                  key: 0,\n                  size: \"small\",\n                  type: \"danger\",\n                  onClick: _ctx.clearAllDataFiles\n                }, {\n                  default: _withCtx(() => _cache[7] || (_cache[7] = [\n                    _createTextVNode(\"清空\")\n                  ])),\n                  _: 1,\n                  __: [7]\n                }, 8, [\"onClick\"]))\n              : _createCommentVNode(\"\", true)\n          ])\n        ]),\n        _createElementVNode(\"div\", _hoisted_16, [\n          _createVNode(_component_el_table, {\n            data: _ctx.importedFiles,\n            border: \"\",\n            style: {\"width\":\"100%\"},\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                label: \"类型\",\n                width: \"80\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createVNode(_component_el_tag, {\n                    type: _ctx.getFileTypeTagType(scope.row.fileType),\n                    size: \"small\"\n                  }, {\n                    default: _withCtx(() => [\n                      _createTextVNode(_toDisplayString(scope.row.fileType.toUpperCase()), 1)\n                    ]),\n                    _: 2\n                  }, 1032, [\"type\"])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"文件名\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createElementVNode(\"div\", {\n                    class: \"file-name\",\n                    title: scope.row.path\n                  }, [\n                    _createElementVNode(\"span\", _hoisted_18, _toDisplayString(scope.row.fileName), 1)\n                  ], 8, _hoisted_17)\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"状态\",\n                width: \"100\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createVNode(_component_el_tag, {\n                    type: _ctx.getStatusTagType(scope.row.status),\n                    size: \"small\"\n                  }, {\n                    default: _withCtx(() => [\n                      _createTextVNode(_toDisplayString(_ctx.getStatusText(scope.row.status)), 1)\n                    ]),\n                    _: 2\n                  }, 1032, [\"type\"])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"操作\",\n                width: \"300\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createElementVNode(\"div\", _hoisted_19, [\n                    (scope.row.status === _ctx.SourceFileStatus.Pending || scope.row.status === _ctx.SourceFileStatus.Error)\n                      ? (_openBlock(), _createBlock(_component_el_button, {\n                          key: 0,\n                          type: \"primary\",\n                          size: \"small\",\n                          onClick: ($event: any) => (_ctx.parseDataFile(scope.row.id))\n                        }, {\n                          default: _withCtx(() => _cache[9] || (_cache[9] = [\n                            _createTextVNode(\" 解析参数 \")\n                          ])),\n                          _: 2,\n                          __: [9]\n                        }, 1032, [\"onClick\"]))\n                      : _createCommentVNode(\"\", true),\n                    (scope.row.status === _ctx.SourceFileStatus.Parsed)\n                      ? (_openBlock(), _createBlock(_component_el_button, {\n                          key: 1,\n                          type: \"success\",\n                          size: \"small\",\n                          onClick: ($event: any) => (_ctx.viewDataFileDetails(scope.row))\n                        }, {\n                          default: _withCtx(() => _cache[10] || (_cache[10] = [\n                            _createTextVNode(\" 查看结果 \")\n                          ])),\n                          _: 2,\n                          __: [10]\n                        }, 1032, [\"onClick\"]))\n                      : _createCommentVNode(\"\", true),\n                    _createVNode(_component_el_button, {\n                      type: \"warning\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.openDataFileFolder(scope.row.path))\n                    }, {\n                      default: _withCtx(() => _cache[11] || (_cache[11] = [\n                        _createTextVNode(\" 打开文件夹 \")\n                      ])),\n                      _: 2,\n                      __: [11]\n                    }, 1032, [\"onClick\"]),\n                    _createVNode(_component_el_button, {\n                      type: \"danger\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.removeDataFile(scope.row.id))\n                    }, {\n                      default: _withCtx(() => _cache[12] || (_cache[12] = [\n                        _createTextVNode(\" 移除 \")\n                      ])),\n                      _: 2,\n                      __: [12]\n                    }, 1032, [\"onClick\"])\n                  ])\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ])\n    ]),\n    _createVNode(_component_ParamParseDialog, {\n      modelValue: _ctx.parseDialogVisible,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.parseDialogVisible) = $event)),\n      \"source-file\": _ctx.currentParseFile,\n      \"parsed-params\": _ctx.currentParsedParams,\n      onApplyParams: _ctx.applyParsedParams\n    }, null, 8, [\"modelValue\", \"source-file\", \"parsed-params\", \"onApplyParams\"])\n  ]))\n}", "<template>\n  <div class=\"parameter-tool\">\n    <!-- 参数编辑主区域 -->\n    <div class=\"main-content\">\n      <!-- CIN 文件操作卡片 -->\n      <div class=\"cin-operations-section\">\n        <div class=\"section-header\">\n          <h4>CIN 文件操作</h4>\n        </div>\n\n        <div class=\"cin-operations-content\">\n          <div class=\"operation-row\">\n            <el-cascader\n              v-model=\"selectedTemplatePath\"\n              :options=\"cascaderOptions\"\n              placeholder=\"选择 CIN 模板\"\n              style=\"width: 300px;\"\n              @change=\"handleTemplateChange\"\n              clearable\n            />\n\n            <el-button @click=\"selectLocalFile\" style=\"margin-left: 16px;\">选择本地 CIN 文件</el-button>\n\n            <el-button type=\"primary\" @click=\"exportCinFile\" :loading=\"processing\" style=\"margin-left: 16px;\">导出CIN</el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 搜索和过滤 -->\n      <div class=\"search-bar\" v-if=\"variables.length > 0\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索参数...\"\n          clearable\n          style=\"width: 300px;\"\n        />\n        <div class=\"filter-info\">\n          <span>显示 {{ filteredVariables.length }} / {{ variables.length }} 个参数</span>\n          <span v-if=\"importedFilesCount > 0\" class=\"import-info\">\n            已导入 {{ importedFilesCount }} 个文件的参数\n          </span>\n        </div>\n      </div>\n\n      <!-- 参数表格 -->\n      <div class=\"parameters-section\">\n        <el-table :data=\"filteredVariables\" border style=\"width: 100%; height: 100%;\" size=\"small\">\n          <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"type\" label=\"类型\" width=\"120\" show-overflow-tooltip />\n          <el-table-column prop=\"value\" label=\"当前值\" width=\"200\" show-overflow-tooltip>\n            <template #default=\"scope\">\n              <span class=\"current-value\">{{ scope.row.value || '(空)' }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"新值\" min-width=\"200\">\n            <template #default=\"scope\">\n              <el-input\n                v-model=\"parameterValues[scope.row.name]\"\n                placeholder=\"输入新值\"\n                size=\"small\"\n                @change=\"onParameterChange(scope.row.name)\"\n              />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"来源\" width=\"150\" show-overflow-tooltip>\n            <template #default=\"scope\">\n              <div v-if=\"getParameterSource(scope.row.name)\" class=\"parameter-source\">\n                <el-icon><document /></el-icon>\n                <span>{{ getParameterSource(scope.row.name) }}</span>\n              </div>\n              <span v-else class=\"no-source\">CIN 文件</span>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <!-- 数据文件管理 -->\n      <div class=\"data-files-section\">\n        <div class=\"section-header\">\n          <h4>数据文件 (ARXML/SDDB/LDF)</h4>\n          <div class=\"header-actions\">\n            <el-button size=\"small\" type=\"primary\" @click=\"selectDataFiles\">添加文件</el-button>\n            <el-button size=\"small\" type=\"danger\" @click=\"clearAllDataFiles\" v-if=\"importedFiles.length > 0\">清空</el-button>\n          </div>\n        </div>\n        \n        <div class=\"data-files-table\">\n          <el-table :data=\"importedFiles\" border style=\"width: 100%\" size=\"small\">\n            <el-table-column label=\"类型\" width=\"80\">\n              <template #default=\"scope\">\n                <el-tag :type=\"getFileTypeTagType(scope.row.fileType)\" size=\"small\">{{ scope.row.fileType.toUpperCase() }}</el-tag>\n              </template>\n            </el-table-column>\n            \n            <el-table-column label=\"文件名\" min-width=\"200\">\n              <template #default=\"scope\">\n                <div class=\"file-name\" :title=\"scope.row.path\">\n                  <span class=\"name\">{{ scope.row.fileName }}</span>\n                </div>\n              </template>\n            </el-table-column>\n            \n            <el-table-column label=\"状态\" width=\"100\">\n              <template #default=\"scope\">\n                <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">{{ getStatusText(scope.row.status) }}</el-tag>\n              </template>\n            </el-table-column>\n            \n            <el-table-column label=\"操作\" width=\"300\">\n              <template #default=\"scope\">\n                <div class=\"action-buttons\">\n                  <el-button \n                    v-if=\"scope.row.status === SourceFileStatus.Pending || scope.row.status === SourceFileStatus.Error\"\n                    type=\"primary\" \n                    size=\"small\"\n                    @click=\"parseDataFile(scope.row.id)\"\n                  >\n                    解析参数\n                  </el-button>\n                  \n                  <el-button \n                    v-if=\"scope.row.status === SourceFileStatus.Parsed\"\n                    type=\"success\" \n                    size=\"small\"\n                    @click=\"viewDataFileDetails(scope.row)\"\n                  >\n                    查看结果\n                  </el-button>\n                  \n                  <el-button\n                    type=\"warning\"\n                    size=\"small\"\n                    @click=\"openDataFileFolder(scope.row.path)\"\n                  >\n                    打开文件夹\n                  </el-button>\n                  \n                  <el-button \n                    type=\"danger\" \n                    size=\"small\"\n                    @click=\"removeDataFile(scope.row.id)\"\n                  >\n                    移除\n                  </el-button>\n                </div>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </div>\n    </div>\n\n    <!-- 参数导入结果弹窗 -->\n    <ParamParseDialog\n      v-model=\"parseDialogVisible\"\n      :source-file=\"currentParseFile\"\n      :parsed-params=\"currentParsedParams\"\n      @apply-params=\"applyParsedParams\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted } from \"vue\";\nimport {\n  appApi,\n  CinTemplate,\n  CaplVariable,\n  CinParameterParseRequest,\n  CinParameterRequest,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus,\n  ParsedParam\n} from \"@/api/appApi\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { Document } from '@element-plus/icons-vue';\nimport ParamParseDialog from \"@/components/ParamParseDialog.vue\";\n\nexport default defineComponent({\n  name: \"ParameterToolView\",\n  components: {\n    ParamParseDialog,\n    Document\n  },\n  setup() {\n    // 数据\n    const templates = ref<CinTemplate[]>([]);\n    const sourceType = ref<string>(\"template\");\n    const selectedCategory = ref<string>(\"\");\n    const selectedTemplateId = ref<string>(\"\");\n    const selectedTemplatePath = ref<string[]>([]);\n    const filePath = ref<string>(\"\");\n    const variables = ref<CaplVariable[]>([]);\n    const parameterValues = ref<{ [key: string]: string }>({});\n    const sourceFilePath = ref<string>(\"\");\n    const searchKeyword = ref<string>(\"\");\n\n    // 状态\n    const parsing = ref(false);\n    const processing = ref(false);\n\n    // 源文件管理相关\n    const parseDialogVisible = ref(false);\n    const currentParseFile = ref<SourceFile | null>(null);\n    const currentParsedParams = ref<ParsedParam[]>([]);\n    const parameterSources = ref<{ [key: string]: string }>({});\n\n    // 新增状态\n    const importedFiles = ref<SourceFile[]>([]);\n    const modifiedParams = ref<Set<string>>(new Set());\n\n    // 计算属性\n    const categories = computed(() => {\n      const categorySet = new Set(templates.value.map(t => t.category));\n      return Array.from(categorySet).filter(c => c);\n    });\n\n    const cascaderOptions = computed(() => {\n      return categories.value.map(category => ({\n        value: category,\n        label: category,\n        children: templates.value\n          .filter(t => t.category === category)\n          .map(template => ({\n            value: template.id,\n            label: template.name\n          }))\n      }));\n    });\n\n    const filteredTemplates = computed(() => {\n      if (!selectedCategory.value) return [];\n      return templates.value.filter(t => t.category === selectedCategory.value);\n    });\n\n    const filteredVariables = computed(() => {\n      if (!searchKeyword.value) return variables.value;\n      const keyword = searchKeyword.value.toLowerCase();\n      return variables.value.filter(v => \n        v.name.toLowerCase().includes(keyword) ||\n        v.type.toLowerCase().includes(keyword) ||\n        (v.value && v.value.toLowerCase().includes(keyword))\n      );\n    });\n\n    const importedFilesCount = computed(() => importedFiles.value.length);\n\n    const modifiedParamsCount = computed(() => modifiedParams.value.size);\n\n    const hasUnsavedChanges = computed(() => modifiedParams.value.size > 0);\n\n    // 方法\n    const loadTemplates = async () => {\n      try {\n        const response = await appApi.cinParameter.getTemplates();\n        templates.value = response.data;\n\n        // 自动选择第一个类别和模板\n        if (categories.value.length > 0) {\n          selectedCategory.value = categories.value[0];\n          onCategoryChange();\n        }\n      } catch (error) {\n        console.error('加载模板失败:', error);\n      }\n    };\n\n    const onSourceTypeChange = () => {\n      // 清空之前的数据\n      variables.value = [];\n      parameterValues.value = {};\n      sourceFilePath.value = \"\";\n      modifiedParams.value.clear();\n    };\n\n    const onCategoryChange = () => {\n      if (filteredTemplates.value.length > 0) {\n        selectedTemplateId.value = filteredTemplates.value[0].id;\n      } else {\n        selectedTemplateId.value = \"\";\n      }\n    };\n\n    const selectLocalFile = async () => {\n      try {\n        const response = await appApi.cinParameter.selectFile();\n        filePath.value = response.data;\n        // 自动解析选择的文件\n        await parseSelectedFile();\n      } catch (error) {\n        console.error('选择文件失败:', error);\n      }\n    };\n\n    const parseSelectedFile = async () => {\n      if (!filePath.value) return;\n      \n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"file\",\n          filePath: filePath.value\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功解析 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('解析文件失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const handleTemplateChange = (value: string[]) => {\n      if (value && value.length === 2) {\n        selectedCategory.value = value[0];\n        selectedTemplateId.value = value[1];\n        loadSelectedTemplate();\n      }\n    };\n\n    const getTemplatesByCategory = (category: string) => {\n      return templates.value.filter(t => t.category === category);\n    };\n\n    const loadSelectedTemplate = async () => {\n      if (!selectedTemplateId.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"template\",\n          templateId: selectedTemplateId.value,\n          filePath: \"\"\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功加载模板 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('加载模板失败:', error);\n        ElMessage.error('加载模板失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const onParameterChange = (paramName: string) => {\n      modifiedParams.value.add(paramName);\n    };\n\n    const exportCinFile = async () => {\n      processing.value = true;\n      try {\n        const request: CinParameterRequest = {\n          sourceType: sourceType.value,\n          templateId: sourceType.value === \"template\" ? selectedTemplateId.value : undefined,\n          filePath: sourceType.value === \"file\" ? filePath.value : \"\",\n          parameterValues: parameterValues.value\n        };\n\n        const response = await appApi.cinParameter.processFile(request);\n        const result = response.data;\n\n        ElMessage.success(`文件导出成功！`);\n        modifiedParams.value.clear();\n\n        await appApi.explorer.openExplorer(result.outputFilePath);\n\n      } catch (error) {\n        console.error('导出文件失败:', error);\n        ElMessage.error('导出文件失败');\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 源文件管理相关方法\n    const handleParseResult = (file: SourceFile, params: ParsedParam[]) => {\n      currentParseFile.value = file;\n      currentParsedParams.value = params;\n      parseDialogVisible.value = true;\n    };\n\n    const applyParsedParams = (params: ParsedParam[]) => {\n      params.forEach(param => {\n        // 查找对应的变量（不区分大小写）\n        const variable = variables.value.find(v => v.name.toLowerCase() === param.name.toLowerCase());\n        if (variable) {\n          // 应用参数值\n          parameterValues.value[variable.name] = String(param.value);\n          // 记录参数来源\n          parameterSources.value[variable.name] = param.source;\n          // 标记为已修改\n          modifiedParams.value.add(variable.name);\n        }\n      });\n\n      // 更新导入文件列表\n      if (currentParseFile.value) {\n        const existingIndex = importedFiles.value.findIndex(f => f.id === currentParseFile.value!.id);\n        if (existingIndex >= 0) {\n          importedFiles.value[existingIndex] = currentParseFile.value;\n        } else {\n          importedFiles.value.push(currentParseFile.value);\n        }\n      }\n\n      ElMessage.success(`成功应用 ${params.length} 个参数`);\n    };\n\n    const getParameterSource = (paramName: string) => {\n      return parameterSources.value[paramName];\n    };\n\n    const getFileName = (path: string) => {\n      if (!path) return '';\n      return path.split(/[\\\\/]/).pop() || '';\n    };\n\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getAppliedParamsCount = (file: SourceFile) => {\n      // 计算该文件已应用的参数数量\n      let count = 0;\n      Object.keys(parameterSources.value).forEach(paramName => {\n        if (parameterSources.value[paramName] === file.fileName) {\n          count++;\n        }\n      });\n      return count;\n    };\n\n    const removeImportedFile = async (fileId: string) => {\n      try {\n        // 调用服务端接口移除文件\n        await appApi.cinParameter.removeSourceFile({ fileId });\n        \n        // 从本地列表中移除文件\n        const fileIndex = importedFiles.value.findIndex(f => f.id === fileId);\n        if (fileIndex >= 0) {\n          importedFiles.value.splice(fileIndex, 1);\n          ElMessage.success('已移除数据文件');\n        }\n      } catch (error) {\n        console.error('移除文件失败:', error);\n        ElMessage.error('移除文件失败');\n      }\n    };\n\n    // 参数数据文件相关方法\n    const selectDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.selectSourceFiles();\n        const filePaths = response.data;\n        \n        if (filePaths && filePaths.length > 0) {\n          await addDataFiles(filePaths);\n          ElMessage.success(`成功添加 ${filePaths.length} 个参数数据文件`);\n        }\n      } catch (error) {\n        console.error('选择文件失败:', error);\n        ElMessage.error('选择文件失败');\n      }\n    };\n\n    const addDataFiles = async (filePaths: string[]) => {\n      try {\n        const response = await appApi.cinParameter.addSourceFiles({ filePaths });\n        const newFiles = response.data;\n        \n        // 更新导入文件列表\n        newFiles.forEach(file => {\n          const existingIndex = importedFiles.value.findIndex(f => f.id === file.id);\n          if (existingIndex >= 0) {\n            importedFiles.value[existingIndex] = file;\n          } else {\n            importedFiles.value.push(file);\n          }\n        });\n        \n        return newFiles;\n      } catch (error) {\n        console.error('添加文件失败:', error);\n        throw error;\n      }\n    };\n\n    const parseDataFile = async (fileId: string) => {\n      try {\n        // 先更新本地状态\n        const file = importedFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Parsing;\n        }\n\n        const response = await appApi.cinParameter.parseSourceFile({\n          fileId\n        });\n        \n        const updatedFile = response.data;\n        \n        // 更新文件列表中的对应项\n        const index = importedFiles.value.findIndex(f => f.id === fileId);\n        if (index >= 0) {\n          importedFiles.value[index] = updatedFile;\n        }\n        \n        ElMessage.success(`参数解析完成，共解析出 ${updatedFile.parsedParams?.length || 0} 个参数`);\n        \n        // 自动弹出参数解析结果对话框\n        if (updatedFile.parsedParams && updatedFile.parsedParams.length > 0) {\n          currentParseFile.value = updatedFile;\n          currentParsedParams.value = updatedFile.parsedParams;\n          parseDialogVisible.value = true;\n        }\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('参数解析失败');\n        \n        // 恢复状态\n        const file = importedFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Error;\n        }\n      }\n    };\n\n    const viewDataFileDetails = (file: SourceFile) => {\n      if (file.parsedParams && file.parsedParams.length > 0) {\n        currentParseFile.value = file;\n        currentParsedParams.value = file.parsedParams;\n        parseDialogVisible.value = true;\n      } else {\n        ElMessage.warning('该文件暂无解析结果');\n      }\n    };\n\n    const openDataFileFolder = async (filePath: string) => {\n      try {\n        await appApi.explorer.openExplorer(filePath);\n      } catch (error) {\n        console.error('打开文件夹失败:', error);\n        ElMessage.error('打开文件夹失败');\n      }\n    };\n\n    const removeDataFile = (fileId: string) => {\n      removeImportedFile(fileId);\n    };\n\n    const clearAllDataFiles = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有参数数据文件吗？', '确认', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n\n        // 调用服务端接口清空所有文件\n        await appApi.cinParameter.clearSourceFiles();\n        \n        // 清空本地文件列表\n        importedFiles.value = [];\n        ElMessage.success('已清空所有参数数据文件');\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('清空文件失败:', error);\n          ElMessage.error('清空文件失败');\n        }\n      }\n    };\n\n    const getStatusTagType = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '';\n        case SourceFileStatus.Parsing: return 'warning';\n        case SourceFileStatus.Parsed: return 'success';\n        case SourceFileStatus.Error: return 'danger';\n        default: return '';\n      }\n    };\n\n    const getStatusText = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '待解析';\n        case SourceFileStatus.Parsing: return '解析中';\n        case SourceFileStatus.Parsed: return '已解析';\n        case SourceFileStatus.Error: return '解析失败';\n        default: return status;\n      }\n    };\n\n    // 加载数据文件列表\n    const loadDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.getSourceFiles();\n        importedFiles.value = response.data;\n      } catch (error) {\n        console.error('加载数据文件失败:', error);\n        ElMessage.error('加载数据文件失败');\n      }\n    };\n\n    onMounted(async () => {\n      await loadTemplates();\n      await loadDataFiles();\n    });\n\n    return {\n      templates,\n      sourceType,\n      selectedCategory,\n      selectedTemplateId,\n      selectedTemplatePath,\n      filePath,\n      variables,\n      parameterValues,\n      sourceFilePath,\n      searchKeyword,\n      parsing,\n      processing,\n      categories,\n      cascaderOptions,\n      filteredTemplates,\n      filteredVariables,\n      importedFilesCount,\n      modifiedParamsCount,\n      hasUnsavedChanges,\n      importedFiles,\n      onSourceTypeChange,\n      onCategoryChange,\n      selectLocalFile,\n      handleTemplateChange,\n      getTemplatesByCategory,\n      loadSelectedTemplate,\n      onParameterChange,\n      exportCinFile,\n      getFileName,\n      getFileTypeTagType,\n      getAppliedParamsCount,\n      removeImportedFile,\n      // 参数数据文件相关\n      selectDataFiles,\n      parseDataFile,\n      viewDataFileDetails,\n      openDataFileFolder,\n      removeDataFile,\n      clearAllDataFiles,\n      getStatusTagType,\n      getStatusText,\n      // 源文件管理相关\n      parseDialogVisible,\n      currentParseFile,\n      currentParsedParams,\n      handleParseResult,\n      applyParsedParams,\n      getParameterSource,\n      // 图标和枚举\n      Document,\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n.parameter-tool {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--el-bg-color-page);\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: var(--el-bg-color);\n  border-bottom: 1px solid var(--el-border-color-base);\n  flex-shrink: 0;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 8px;\n}\n\n.current-file-info {\n  padding: 8px 20px;\n  background: var(--el-color-primary-light-9);\n  border-bottom: 1px solid var(--el-border-color-lighter);\n  flex-shrink: 0;\n}\n\n.file-info-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.file-icon {\n  color: var(--el-color-primary);\n}\n\n.file-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n}\n\n.main-content {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n}\n\n.search-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.filter-info {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: var(--el-text-color-secondary);\n}\n\n.import-info {\n  color: var(--el-color-primary);\n}\n\n.parameters-section {\n  flex: 1;\n  overflow: hidden;\n}\n\n.current-value {\n  color: var(--el-text-color-regular);\n  font-style: italic;\n}\n\n.parameter-source {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: var(--el-color-primary);\n}\n\n.no-source {\n  color: var(--el-text-color-placeholder);\n  font-style: italic;\n}\n\n/* CIN 操作卡片样式 */\n.cin-operations-section {\n  margin-bottom: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex-shrink: 0;\n}\n\n.cin-operations-content {\n  margin-top: 12px;\n}\n\n.operation-row {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n/* 参数数据文件样式 */\n.data-files-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex-shrink: 0;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.data-files-table {\n  margin-top: 12px;\n}\n\n.data-files-table .file-name .name {\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  flex-wrap: wrap;\n}\n\n.imported-files-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n}\n\n.imported-files-section h4 {\n  margin: 0 0 12px 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.imported-files-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.imported-file-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: var(--el-fill-color-light);\n  border-radius: 4px;\n}\n\n.imported-file-item .file-name {\n  font-weight: 500;\n}\n\n.param-count {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n.file-actions {\n  margin-left: auto;\n  display: flex;\n  gap: 8px;\n}\n\n.template-selection {\n  display: flex;\n}\n\n.category-list {\n  width: 200px;\n  border-right: 1px solid var(--el-border-color-lighter);\n  overflow-y: auto;\n}\n\n.category-item {\n  padding: 12px 16px;\n  cursor: pointer;\n  border-bottom: 1px solid var(--el-border-color-lighter);\n}\n\n.category-item:hover {\n  background: var(--el-fill-color-light);\n}\n\n.category-item.active {\n  background: var(--el-color-primary-light-9);\n  color: var(--el-color-primary);\n  font-weight: 500;\n}\n\n.template-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n.template-item {\n  padding: 12px;\n  margin-bottom: 8px;\n  border: 1px solid var(--el-border-color-lighter);\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.template-item:hover {\n  border-color: var(--el-color-primary);\n}\n\n.template-item.active {\n  border-color: var(--el-color-primary);\n  background: var(--el-color-primary-light-9);\n}\n\n.template-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n  margin-bottom: 4px;\n}\n\n.template-desc {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .toolbar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n  \n  .toolbar-left,\n  .toolbar-right {\n    justify-content: center;\n  }\n  \n  .search-bar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n  \n  .template-selection {\n    flex-direction: column;\n    height: auto;\n  }\n  \n  .category-list {\n    width: auto;\n    border-right: none;\n    border-bottom: 1px solid var(--el-border-color-lighter);\n    max-height: 150px;\n  }\n}\n</style>\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, unref as _unref, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"dialog-content\" }\nconst _hoisted_2 = { class: \"filter-section\" }\nconst _hoisted_3 = { class: \"param-list\" }\nconst _hoisted_4 = { class: \"param-value\" }\nconst _hoisted_5 = { key: 0 }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"batch-actions\"\n}\nconst _hoisted_7 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamParseDialog',\n  props: {\n    modelValue: { type: Boolean },\n    sourceFile: {},\n    parsedParams: {}\n  },\n  emits: [\"update:modelValue\", \"apply-params\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst jsonDialogVisible = ref(false);\nconst jsonValue = ref('');\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p => \n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      selectAllCurrentEcuParams();\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  selectedParams.value = [...filteredParams.value];\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst getParamTypeTagType = (paramType: ParamType) => {\n  switch (paramType) {\n    case ParamType.String: return '';\n    case ParamType.Integer: return 'success';\n    case ParamType.Double: return 'success';\n    case ParamType.Boolean: return 'warning';\n    case ParamType.Json: return 'primary';\n    case ParamType.Array: return 'info';\n    case ParamType.Object: return 'info';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  if (typeof value === 'string') {\n    return value.length > 50 ? value.substring(0, 50) + '...' : value;\n  }\n  \n  return String(value);\n};\n\nconst showJsonValue = (param: ParsedParam) => {\n  try {\n    if (typeof param.value === 'string') {\n      // 尝试格式化JSON字符串\n      const parsed = JSON.parse(param.value);\n      jsonValue.value = JSON.stringify(parsed, null, 2);\n    } else {\n      // 直接序列化对象\n      jsonValue.value = JSON.stringify(param.value, null, 2);\n    }\n  } catch {\n    // 如果不是有效的JSON，直接显示原值\n    jsonValue.value = String(param.value);\n  }\n  \n  jsonDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_alert = _resolveComponent(\"el-alert\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((visible).value = $event)),\n    title: \"参数解析结果\",\n    width: \"80%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_7, [\n        _createVNode(_component_el_button, { onClick: handleClose }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [\n            _createTextVNode(\"取消\")\n          ])),\n          _: 1,\n          __: [6]\n        }),\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: applySelectedParams,\n          disabled: selectedParams.value.length === 0\n        }, {\n          default: _withCtx(() => [\n            _createTextVNode(\" 应用参数 (\" + _toDisplayString(selectedParams.value.length) + \") \", 1)\n          ]),\n          _: 1\n        }, 8, [\"disabled\"])\n      ])\n    ]),\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createVNode(_component_el_select, {\n            modelValue: selectedEcu.value,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedEcu).value = $event)),\n            placeholder: \"选择 ECU\",\n            style: {\"width\":\"200px\"},\n            onChange: handleEcuChange\n          }, {\n            default: _withCtx(() => [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(ecuList.value, (ecu) => {\n                return (_openBlock(), _createBlock(_component_el_option, {\n                  key: ecu,\n                  label: ecu,\n                  value: ecu\n                }, null, 8, [\"label\", \"value\"]))\n              }), 128))\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"]),\n          _createVNode(_component_el_input, {\n            modelValue: searchText.value,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((searchText).value = $event)),\n            placeholder: \"搜索参数名称...\",\n            clearable: \"\",\n            style: {\"width\":\"300px\"}\n          }, {\n            prefix: _withCtx(() => [\n              _createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [\n                  _createVNode(_unref(Search))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"])\n        ]),\n        _createElementVNode(\"div\", _hoisted_3, [\n          _createVNode(_component_el_table, {\n            data: filteredParams.value,\n            height: \"400\",\n            onSelectionChange: handleSelectionChange,\n            \"row-key\": \"name\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                type: \"selection\",\n                width: \"55\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"paramType\",\n                label: \"类型\",\n                width: \"100\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createVNode(_component_el_tag, {\n                    type: getParamTypeTagType(row.paramType),\n                    size: \"small\"\n                  }, {\n                    default: _withCtx(() => [\n                      _createTextVNode(_toDisplayString(row.paramType), 1)\n                    ]),\n                    _: 2\n                  }, 1032, [\"type\"])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"value\",\n                label: \"值\",\n                \"min-width\": \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createElementVNode(\"div\", _hoisted_4, [\n                    (row.paramType !== 'Json')\n                      ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, _toDisplayString(formatValue(row.value)), 1))\n                      : (_openBlock(), _createBlock(_component_el_button, {\n                          key: 1,\n                          type: \"text\",\n                          size: \"small\",\n                          onClick: ($event: any) => (showJsonValue(row))\n                        }, {\n                          default: _withCtx(() => _cache[5] || (_cache[5] = [\n                            _createTextVNode(\" 查看JSON \")\n                          ])),\n                          _: 2,\n                          __: [5]\n                        }, 1032, [\"onClick\"]))\n                  ])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"description\",\n                label: \"描述\",\n                \"min-width\": \"150\",\n                \"show-overflow-tooltip\": \"\"\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ]),\n        (selectedParams.value.length > 0)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n              _createVNode(_component_el_alert, {\n                title: `已选择 ${selectedParams.value.length} 个参数`,\n                type: \"info\",\n                closable: false,\n                \"show-icon\": \"\"\n              }, null, 8, [\"title\"])\n            ]))\n          : _createCommentVNode(\"\", true)\n      ]),\n      _createVNode(_component_el_dialog, {\n        modelValue: jsonDialogVisible.value,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((jsonDialogVisible).value = $event)),\n        title: \"JSON 参数值\",\n        width: \"60%\",\n        \"append-to-body\": \"\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_input, {\n            modelValue: jsonValue.value,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((jsonValue).value = $event)),\n            type: \"textarea\",\n            rows: 15,\n            readonly: \"\",\n            style: {\"font-family\":\"'Courier New', monospace\"}\n          }, null, 8, [\"modelValue\"])\n        ]),\n        _: 1\n      }, 8, [\"modelValue\"])\n    ]),\n    _: 1\n  }, 8, [\"modelValue\"]))\n}\n}\n\n})", "<template>\n  <el-dialog\n    v-model=\"visible\"\n    title=\"参数解析结果\"\n    width=\"80%\"\n    :before-close=\"handleClose\"\n    destroy-on-close\n  >\n    <div class=\"dialog-content\">\n      <!-- ECU选择和搜索 -->\n      <div class=\"filter-section\">\n        <el-select \n          v-model=\"selectedEcu\" \n          placeholder=\"选择 ECU\" \n          style=\"width: 200px;\"\n          @change=\"handleEcuChange\"\n        >\n          <el-option\n            v-for=\"ecu in ecuList\"\n            :key=\"ecu\"\n            :label=\"ecu\"\n            :value=\"ecu\"\n          />\n        </el-select>\n        \n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索参数名称...\"\n          clearable\n          style=\"width: 300px\"\n        >\n          <template #prefix>\n            <el-icon><Search /></el-icon>\n          </template>\n        </el-input>\n      </div>\n\n      <!-- 参数列表 -->\n      <div class=\"param-list\">\n        <el-table\n          :data=\"filteredParams\"\n          height=\"400\"\n          @selection-change=\"handleSelectionChange\"\n          row-key=\"name\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"paramType\" label=\"类型\" width=\"100\">\n            <template #default=\"{ row }\">\n              <el-tag :type=\"getParamTypeTagType(row.paramType)\" size=\"small\">\n                {{ row.paramType }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"value\" label=\"值\" min-width=\"200\" show-overflow-tooltip>\n            <template #default=\"{ row }\">\n              <div class=\"param-value\">\n                <span v-if=\"row.paramType !== 'Json'\">{{ formatValue(row.value) }}</span>\n                <el-button \n                  v-else \n                  type=\"text\" \n                  size=\"small\"\n                  @click=\"showJsonValue(row)\"\n                >\n                  查看JSON\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"description\" label=\"描述\" min-width=\"150\" show-overflow-tooltip />\n        </el-table>\n      </div>\n\n      <!-- 批量操作 -->\n      <div class=\"batch-actions\" v-if=\"selectedParams.length > 0\">\n        <el-alert \n          :title=\"`已选择 ${selectedParams.length} 个参数`\" \n          type=\"info\" \n          :closable=\"false\"\n          show-icon\n        />\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button \n          type=\"primary\" \n          @click=\"applySelectedParams\"\n          :disabled=\"selectedParams.length === 0\"\n        >\n          应用参数 ({{ selectedParams.length }})\n        </el-button>\n      </div>\n    </template>\n\n    <!-- JSON 查看弹窗 -->\n    <el-dialog\n      v-model=\"jsonDialogVisible\"\n      title=\"JSON 参数值\"\n      width=\"60%\"\n      append-to-body\n    >\n      <el-input\n        v-model=\"jsonValue\"\n        type=\"textarea\"\n        :rows=\"15\"\n        readonly\n        style=\"font-family: 'Courier New', monospace;\"\n      />\n    </el-dialog>\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\nconst props = defineProps<Props>();\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'apply-params': [params: ParsedParam[]];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst jsonDialogVisible = ref(false);\nconst jsonValue = ref('');\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p => \n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      selectAllCurrentEcuParams();\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  selectedParams.value = [...filteredParams.value];\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst getParamTypeTagType = (paramType: ParamType) => {\n  switch (paramType) {\n    case ParamType.String: return '';\n    case ParamType.Integer: return 'success';\n    case ParamType.Double: return 'success';\n    case ParamType.Boolean: return 'warning';\n    case ParamType.Json: return 'primary';\n    case ParamType.Array: return 'info';\n    case ParamType.Object: return 'info';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  if (typeof value === 'string') {\n    return value.length > 50 ? value.substring(0, 50) + '...' : value;\n  }\n  \n  return String(value);\n};\n\nconst showJsonValue = (param: ParsedParam) => {\n  try {\n    if (typeof param.value === 'string') {\n      // 尝试格式化JSON字符串\n      const parsed = JSON.parse(param.value);\n      jsonValue.value = JSON.stringify(parsed, null, 2);\n    } else {\n      // 直接序列化对象\n      jsonValue.value = JSON.stringify(param.value, null, 2);\n    }\n  } catch {\n    // 如果不是有效的JSON，直接显示原值\n    jsonValue.value = String(param.value);\n  }\n  \n  jsonDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n</script>\n\n<style scoped>\n.dialog-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.filter-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n}\n\n.param-list {\n  border: 1px solid var(--el-border-color-base);\n  border-radius: 6px;\n}\n\n.param-value {\n  max-width: 200px;\n  word-break: break-all;\n}\n\n.batch-actions {\n  margin-top: 8px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n}\n</style>\n", "import script from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamParseDialog.vue?vue&type=style&index=0&id=07b1abc3&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-07b1abc3\"]])\n\nexport default __exports__", "import { render } from \"./ParameterToolView.vue?vue&type=template&id=043d8365&scoped=true&ts=true\"\nimport script from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\nexport * from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\n\nimport \"./ParameterToolView.vue?vue&type=style&index=0&id=043d8365&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-043d8365\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "key", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_cascader", "_resolveComponent", "_component_el_button", "_component_el_input", "_component_el_table_column", "_component_document", "_component_el_icon", "_component_el_table", "_component_el_tag", "_component_ParamParseDialog", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "modelValue", "selectedTemplatePath", "$event", "options", "cascaderOptions", "placeholder", "style", "onChange", "handleTemplateChange", "clearable", "onClick", "selectLocalFile", "default", "_withCtx", "_createTextVNode", "_", "__", "type", "exportCinFile", "loading", "processing", "variables", "length", "searchKeyword", "_toDisplayString", "filteredVariables", "importedFilesCount", "_createCommentVNode", "data", "border", "size", "prop", "label", "width", "scope", "row", "value", "parameterValues", "name", "onParameterChange", "getParameterSource", "selectDataFiles", "importedFiles", "_createBlock", "clearAllDataFiles", "getFileTypeTagType", "fileType", "toUpperCase", "title", "path", "fileName", "getStatusTagType", "status", "getStatusText", "SourceFileStatus", "Pending", "Error", "parseDataFile", "id", "Parsed", "viewDataFileDetails", "openDataFileFolder", "removeDataFile", "parseDialogVisible", "currentParseFile", "currentParsedParams", "onApplyParams", "applyParsedParams", "_defineComponent", "__name", "props", "Boolean", "sourceFile", "parsedParams", "emits", "setup", "__props", "emit", "__emit", "visible", "ref", "searchText", "<PERSON><PERSON><PERSON>", "selectedPara<PERSON>", "jsonDialogVisible", "jsonValue", "ecuList", "computed", "ecus", "Set", "map", "p", "ecuName", "Array", "from", "sort", "filteredParams", "params", "filter", "search", "toLowerCase", "includes", "description", "watch", "newVal", "selectAllCurrentEcuParams", "handleEcuChange", "handleClose", "handleSelectionChange", "selection", "getParamTypeTagType", "paramType", "ParamType", "String", "Integer", "Double", "Json", "Object", "formatValue", "undefined", "substring", "showJsonValue", "param", "parsed", "JSON", "parse", "stringify", "applySelectedParams", "ElMessage", "success", "warning", "_component_el_option", "_component_el_select", "_component_el_alert", "_component_el_dialog", "footer", "disabled", "_Fragment", "_renderList", "ecu", "prefix", "_unref", "Search", "height", "onSelectionChange", "closable", "rows", "readonly", "__exports__", "defineComponent", "components", "ParamParseDialog", "Document", "templates", "sourceType", "selectedCate<PERSON><PERSON>", "selectedTemplateId", "filePath", "sourceFilePath", "parsing", "parameterSources", "modifiedParams", "categories", "categorySet", "t", "category", "c", "children", "template", "filteredTemplates", "keyword", "v", "modifiedParamsCount", "hasUnsavedChanges", "loadTemplates", "async", "response", "appApi", "cinParameter", "getTemplates", "onCategoryChange", "error", "console", "onSourceTypeChange", "clear", "selectFile", "parseSelectedFile", "request", "parseFile", "result", "for<PERSON>ach", "variable", "loadSelectedTemplate", "getTemplatesByCategory", "templateId", "paramName", "add", "processFile", "explorer", "openExplorer", "outputFilePath", "handleParseResult", "file", "find", "source", "existingIndex", "findIndex", "f", "push", "getFileName", "split", "pop", "SourceFileType", "Arxml", "Sddb", "Ldf", "getAppliedParamsCount", "count", "keys", "removeImportedFile", "removeSourceFile", "fileId", "fileIndex", "splice", "selectSourceFiles", "filePaths", "addDataFiles", "addSourceFiles", "newFiles", "Parsing", "parseSourceFile", "updatedFile", "index", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "clearSourceFiles", "loadDataFiles", "getSourceFiles", "onMounted"], "sourceRoot": ""}